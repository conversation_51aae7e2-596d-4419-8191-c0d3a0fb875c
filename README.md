# 🏠 Aqar Bot - نظام إدارة العقارات الذكي

## 📋 نظرة عامة

Aqar Bot هو نظام ذكي لإدارة ومعالجة البيانات العقارية باستخدام Google Apps Script. يقوم النظام بتلقي البيانات من Google Forms، معالجتها باستخدام الذكاء الاصطناعي، وإرسال الإشعارات عبر Google Chat.

## 🎯 المرحلة الأولى - الميزات الحالية

### ✅ الوظائف المكتملة

- **استقبال البيانات**: تلقي البيانات من Google Forms تلقائياً
- **توليد كود الوحدة**: إنشاء كود فريد لكل عقار بتنسيق `DD.MM.YYYY-NNN`
- **القيم الافتراضية**: تطبيق قيم افتراضية للحقول الفارغة
- **تقسيم البيانات المتعددة**: فصل البيانات المتعددة إلى عقارات منفصلة
- **نظام التحقق**: فحص الحقول الإلزامية وتصنيف العقارات
- **نظام الإشعارات**: إرسال إشعارات عبر Google Chat (محاكاة)
- **سجل الإشعارات**: تتبع جميع الإشعارات المرسلة

### 🔄 الوظائف قيد التطوير

- **تحليل الذكاء الاصطناعي**: معالجة البيانات عبر منصات AI متعددة
- **المعالجة المحلية**: تحليل البيانات محلياً كبديل
- **تكامل Google Chat**: الاتصال الفعلي بـ Google Chat API
- **تكامل Notion & Zoho**: تخزين البيانات في الأنظمة الخارجية

## 📁 هيكل المشروع

```
aqar_bot/
├── main.js                 # الكود الرئيسي ونقاط الدخول
├── utils.js                # دوال المساعدة العامة
├── validation.js           # دوال التحقق والفحص
├── notifications.js        # نظام الإشعارات
├── setup_properties.js     # إعداد خصائص السكريبت
├── test_phase1.js          # اختبارات المرحلة الأولى
├── .env                    # متغيرات البيئة (لا يُرفع للمستودع)
├── مراحل تسجيل العقار.txt   # وثائق المشروع
└── README.md               # هذا الملف
```

## 🚀 التثبيت والإعداد

### 1. إنشاء مشروع Google Apps Script

1. اذهب إلى [Google Apps Script](https://script.google.com)
2. أنشئ مشروع جديد
3. انسخ محتوى الملفات إلى المشروع

### 2. إعداد الخصائص

```javascript
// تشغيل هذه الدالة مرة واحدة لإعداد جميع الخصائص
setupAllProperties();
```

### 3. إعداد المشغلات (Triggers)

1. اذهب إلى "Triggers" في محرر Apps Script
2. أضف مشغل جديد:
   - **Function**: `onFormSubmit`
   - **Event source**: From form
   - **Event type**: On form submit

### 4. اختبار النظام

```javascript
// اختبار الإعدادات
testProperties();

// اختبار النظام الكامل
runAllPhase1Tests();

// إعداد النظام
setupSystem();
```

## 📊 البيانات والحقول

### الحقول الأساسية

| الحقل | النوع | الوصف | القيمة الافتراضية |
|-------|--------|--------|-------------------|
| كود الوحدة | نص | كود فريد للعقار | يُولد تلقائياً |
| المنطقة | اختيار | منطقة العقار | غير محدد |
| نوع الوحدة | اختيار | نوع العقار | غير محدد |
| حالة الوحدة | متعدد الاختيار | حالة العقار | غير محدد |
| السعر | رقم | سعر العقار | 0 |
| المساحة | رقم | مساحة العقار | 0 |
| اسم المالك | نص | اسم مالك العقار | غير محدد |
| رقم المالك | رقم | رقم هاتف المالك | 01000000000 |
| حالات النجاح | متعدد الاختيار | مراحل المعالجة | تخزين |

### أنواع البيانات المدعومة

1. **🪀 بيانات متعددة غير مفصله**: عدة عقارات في نص واحد
2. **🪀 بيان عقار غير مفصل**: عقار واحد غير مفصل
3. **💘 بيان مفصل**: عقار واحد مفصل بالكامل

## 🔧 الدوال الرئيسية

### main.js

- `onFormSubmit(e)`: معالجة النماذج الجديدة
- `aiProcessor()`: معالجة الذكاء الاصطناعي
- `fallbackProcessor()`: المعالجة المحلية البديلة
- `setupSystem()`: إعداد النظام

### utils.js

- `generateUnitCode()`: توليد كود الوحدة
- `splitMultiValueField()`: تقسيم البيانات المتعددة
- `getHeadersMap()`: قراءة خريطة العناوين
- `setDefaultValues()`: تطبيق القيم الافتراضية

### validation.js

- `checkAndMarkDamaged()`: فحص العقارات التالفة
- `updatePropertyStatus()`: تحديث حالة العقار
- `validateRequiredFields()`: التحقق من الحقول الإلزامية
- `classifyProperty()`: تصنيف العقارات

### notifications.js

- `sendOrUpdateNotification()`: إرسال/تحديث الإشعارات
- `buildChatCardPayload()`: بناء رسائل Google Chat
- `updateNotificationLog()`: إدارة سجل الإشعارات

## 📱 نظام الإشعارات

### أنواع الإشعارات

1. **📥 تخزين**: تم استقبال العقار
2. **🔍 تفكيك**: تم تحليل العقار
3. **✅ نجاح**: تم تخزين العقار بنجاح
4. **❌ فشل**: فشل في معالجة العقار
5. **🗑️ تالف**: عقار يحتوي على حقول مفقودة

### تنسيق الإشعارات

```json
{
  "cards": [{
    "header": {
      "title": "✅ عقار ناجح",
      "subtitle": "كود الوحدة: 31.01.2025-001"
    },
    "sections": [{
      "widgets": [{
        "textParagraph": {
          "text": "تفاصيل العقار..."
        }
      }]
    }]
  }]
}
```

## 🧪 الاختبارات

### تشغيل جميع الاختبارات

```javascript
runAllPhase1Tests();
```

### اختبارات محددة

```javascript
// اختبار توليد كود الوحدة
testUnitCodeGeneration();

// اختبار تقسيم البيانات
testMultiValueSplit();

// اختبار التحقق من الحقول
testRequiredFieldsValidation();
```

## 📈 المراحل القادمة

### المرحلة الثانية
- تكامل منصات الذكاء الاصطناعي
- تحليل البيانات النصية
- استخراج المعلومات تلقائياً

### المرحلة الثالثة
- تكامل Notion Database
- تكامل Zoho CRM
- تصنيف العقارات المتقدم

### المرحلة الرابعة
- واجهة مستخدم ويب
- تقارير وإحصائيات
- نظام إدارة المستخدمين

## 🔒 الأمان والخصوصية

- جميع المفاتيح محفوظة في Script Properties
- لا يتم تخزين بيانات حساسة في الكود
- تشفير الاتصالات مع الخدمات الخارجية

## 🐛 استكشاف الأخطاء

### مشاكل شائعة

1. **خطأ في الوصول للشيت**
   ```javascript
   // تحقق من صحة SHEET_ID
   testProperties();
   ```

2. **فشل في توليد كود الوحدة**
   ```javascript
   // تحقق من وجود عمود "كود الوحدة"
   const headers = getHeadersMap(sheet);
   Logger.log(headers);
   ```

3. **مشاكل في الإشعارات**
   ```javascript
   // تحقق من إعدادات سجل الإشعارات
   createNotificationLogSheet();
   ```

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:

1. راجع ملف `مراحل تسجيل العقار.txt` للتفاصيل الكاملة
2. شغل `testSystem()` للتحقق من حالة النظام
3. راجع سجلات Logger في Google Apps Script

## 📄 الترخيص

هذا المشروع مطور خصيصاً لشركة Elsweedy Real Estate.

---

**تم تطوير المرحلة الأولى بنجاح ✅**

*آخر تحديث: 31 يناير 2025*
