# 🚀 دليل الإعداد السريع - <PERSON><PERSON>r Bot

## 📋 المتطلبات الأساسية

### 1. Google Apps Script
- حساب Google
- مشروع Google Apps Script جديد

### 2. Google Sheets
- ملف Google Sheets للبيانات الرئيسية
- ملف Google Sheets لسجل الإشعارات

### 3. Google Forms
- نموذج Google Forms مرتبط بالشيت

### 4. Telegram Bot
- إنشاء بوت عبر [@BotFather](https://t.me/BotFather)
- الحصول على Bot Token
- إضافة البوت إلى القناة/المجموعة
- الحصول على Chat ID

### 5. Discord Webhook
- إنشاء Webhook في قناة Discord
- الحصول على Webhook URL

## 🔧 خطوات الإعداد

### الخطوة 1: إنشاء مشروع Google Apps Script

1. اذه<PERSON> إلى [script.google.com](https://script.google.com)
2. انقر على "مشروع جديد"
3. غيّر اسم المشروع إلى "Aqar Bot"

### الخطوة 2: إضافة الملفات

انسخ محتوى كل ملف إلى ملف منفصل في المشروع:

1. **main.js** - الكود الرئيسي
2. **utils.js** - دوال المساعدة
3. **validation.js** - دوال التحقق
4. **notifications.js** - نظام الإشعارات
5. **setup_properties.js** - إعداد الخصائص
6. **test_phase1.js** - اختبارات المرحلة الأولى
7. **test_notifications.js** - اختبارات الإشعارات

### الخطوة 3: إعداد الخصائص

```javascript
// تشغيل هذه الدالة مرة واحدة
setupAllProperties();
```

### الخطوة 4: إعداد المشغلات (Triggers)

1. في محرر Apps Script، اذهب إلى "Triggers"
2. انقر على "Add Trigger"
3. اختر:
   - **Function**: `onFormSubmit`
   - **Event source**: From form
   - **Event type**: On form submit
   - **Form**: اختر النموذج الخاص بك

### الخطوة 5: اختبار النظام

```javascript
// اختبار الإعدادات
testProperties();

// اختبار النظام الكامل
runAllPhase1Tests();

// اختبار الإشعارات
runAllNotificationTests();

// إعداد النظام
setupSystem();
```

## 🔑 الإعدادات المطلوبة

### Google Services
```
SHEET_ID=your_main_sheet_id
SHEET_NAME=Aqar_bot
NOTIFICATIONS_LOG_SHEET_ID=your_log_sheet_id
GOOGLE_FORM_ID=your_form_id
```

### Telegram
```
TELEGRAM_CONTROLER_BOT_TOKEN=your_bot_token
TELEGRAM_ADMIN_CHAT_ID=your_chat_id
```

### Discord
```
DISCORD_WEBHOOK_URL=your_webhook_url
DISCORD_CHANNEL_ID=your_channel_id
```

## 🧪 اختبار الإعداد

### 1. اختبار الإعدادات الأساسية
```javascript
testProperties();
```

### 2. اختبار إعدادات Telegram
```javascript
testTelegramSettings();
```

### 3. اختبار إعدادات Discord
```javascript
testDiscordSettings();
```

### 4. إرسال إشعار اختبار
```javascript
// ⚠️ سيرسل إشعار فعلي!
sendTestNotification();
```

## 🔍 استكشاف الأخطاء

### مشكلة: "معرف الشيت غير موجود"
```javascript
// تحقق من SHEET_ID
const properties = PropertiesService.getScriptProperties();
Logger.log(properties.getProperty('SHEET_ID'));
```

### مشكلة: "فشل إرسال Telegram"
```javascript
// تحقق من إعدادات Telegram
testTelegramSettings();
```

### مشكلة: "فشل إرسال Discord"
```javascript
// تحقق من إعدادات Discord
testDiscordSettings();
```

### مشكلة: "خطأ في توليد كود الوحدة"
```javascript
// تحقق من وجود عمود "كود الوحدة"
const sheet = SpreadsheetApp.openById('your_sheet_id').getSheetByName('Aqar_bot');
const headers = getHeadersMap(sheet);
Logger.log(headers);
```

## 📝 ملاحظات مهمة

### الأمان
- لا تشارك Bot Tokens أو Webhook URLs
- استخدم Script Properties لتخزين المفاتيح الحساسة
- لا تضع المفاتيح في الكود مباشرة

### الأداء
- النظام يدعم معالجة البيانات المتعددة
- يتم تحديث الرسائل الموجودة بدلاً من إرسال رسائل جديدة
- سجل الإشعارات يتتبع جميع الرسائل المرسلة

### التطوير
- استخدم دوال الاختبار قبل التطبيق الفعلي
- راجع سجلات Logger للتشخيص
- ابدأ بإشعارات الاختبار قبل الاستخدام الفعلي

## 🎯 الخطوات التالية

بعد إكمال الإعداد:

1. **اختبر النظام** بإرسال نموذج تجريبي
2. **راقب الإشعارات** في Telegram و Discord
3. **تحقق من سجل الإشعارات** في Google Sheets
4. **استعد للمرحلة الثانية** (تكامل الذكاء الاصطناعي)

## 📞 الدعم

إذا واجهت مشاكل:

1. شغّل `testSystem()` للتحقق من حالة النظام
2. راجع سجلات Logger في Google Apps Script
3. تأكد من صحة جميع المعرفات والمفاتيح
4. اختبر كل مكون على حدة

---

**تم إعداد النظام بنجاح؟ 🎉**

الآن يمكنك البدء في استقبال ومعالجة البيانات العقارية!

*آخر تحديث: 31 يناير 2025*
