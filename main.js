/**
 * ===================================================================
 * AQAR BOT - MAIN ENTRY POINT
 * ===================================================================
 * 
 * المرحلة الأولى من مشروع Aqar Bot العقاري
 * هذا الملف يحتوي على نقاط الدخول الرئيسية للنظام
 * 
 * الوظائف الرئيسية:
 * - onFormSubmit: معالجة البيانات الواردة من Google Form
 * - aiProcessor: معالجة البيانات بالذكاء الاصطناعي
 * - fallbackProcessor: المعالجة المحلية كبديل
 * 
 * <AUTHOR> Bot Team
 * @version 1.0.0 - المرحلة الأولى
 * @created 2025-01-31
 * ===================================================================
 */

/**
 * نقطة الدخول الرئيسية عند إرسال نموذج جديد
 * يتم استدعاؤها تلقائياً عند كل إرسال للنموذج
 *
 * @param {Object} e - كائن الحدث من Google Forms
 */
function onFormSubmit(e) {
  try {
    Logger.log('=== بدء معالجة نموذج جديد ===');

    // 1. الحصول على الشيت الرئيسي
    const sheetId = getMainSheetId();
    const sheetName = getMainSheetName();

    if (!sheetId) {
      throw new Error('معرف الشيت الرئيسي غير موجود في الإعدادات');
    }

    const spreadsheet = SpreadsheetApp.openById(sheetId);
    const sheet = spreadsheet.getSheetByName(sheetName);

    if (!sheet) {
      throw new Error(`ورقة العمل ${sheetName} غير موجودة`);
    }

    // 2. قراءة خريطة العناوين
    const headers = getHeadersMap(sheet);
    Logger.log(`تم قراءة ${Object.keys(headers).length} عنوان من الشيت`);

    // 3. الحصول على الصف الجديد
    const newRow = sheet.getLastRow();
    Logger.log(`معالجة الصف رقم: ${newRow}`);

    // 4. تطبيق القيم الافتراضية
    setDefaultValues(sheet, newRow, headers);
    Logger.log('تم تطبيق القيم الافتراضية');

    // 5. توليد كود الوحدة
    const unitCodeCol = headers['كود الوحدة'];
    if (unitCodeCol) {
      const unitCode = generateUnitCode(sheet, unitCodeCol);
      sheet.getRange(newRow, unitCodeCol).setValue(unitCode);
      Logger.log(`تم توليد كود الوحدة: ${unitCode}`);

      // 6. تحديث حالة النجاح إلى "تخزين"
      updateSuccessStates(sheet, newRow, headers, 'تخزين');
      Logger.log('تم تحديث حالة النجاح إلى "تخزين"');

      // 7. تحديد نوع البيان
      const formData = getFormDataFromRow(sheet, newRow, headers);
      const statementType = determineStatementType(formData, headers);
      Logger.log(`نوع البيان: ${statementType}`);

      // 8. معالجة البيانات حسب النوع
      if (statementType === 'multiple') {
        processMultipleStatements(sheet, newRow, headers, formData);
      } else if (statementType === 'single') {
        processSingleStatement(sheet, newRow, headers, formData, unitCode);
      } else {
        // بيان مفصل - معالجة مباشرة
        processDetailedStatement(sheet, newRow, headers, formData, unitCode);
      }

      // 9. إرسال الإشعار الأولي
      sendOrUpdateNotification(unitCode, 'تخزين', 'تم استقبال البيان بنجاح', true);
      Logger.log('تم إرسال الإشعار الأولي');

    } else {
      throw new Error('عمود كود الوحدة غير موجود في الشيت');
    }

    Logger.log('=== انتهاء معالجة النموذج بنجاح ===');

  } catch (error) {
    Logger.log('خطأ في onFormSubmit: ' + error.toString());

    // إرسال إشعار فشل
    try {
      const errorCode = `ERROR_${Date.now()}`;
      sendOrUpdateNotification(errorCode, 'فشل', `خطأ في معالجة النموذج: ${error.toString()}`, true);
    } catch (notificationError) {
      Logger.log('خطأ في إرسال إشعار الفشل: ' + notificationError.toString());
    }
  }
}

/**
 * معالج الذكاء الاصطناعي - يعمل بجدولة زمنية
 * يقرأ البيانات غير المفككة ويرسلها لمنصات الذكاء الاصطناعي
 */
function aiProcessor() {
  try {
    Logger.log('=== بدء معالجة الذكاء الاصطناعي ===');
    
    // TODO: سيتم تطوير هذه الدالة في مرحلة لاحقة
    // 1. قراءة الصفوف غير المفككة
    // 2. محاولة التحليل عبر منصات الذكاء الاصطناعي
    // 3. تحديث البيانات عند النجاح
    // 4. تحديث حالة النجاح إلى "تفكيك"
    // 5. إرسال إشعار التحديث
    
    Logger.log('معالج الذكاء الاصطناعي - في انتظار التطوير');
    
  } catch (error) {
    Logger.log('خطأ في aiProcessor: ' + error.toString());
  }
}

/**
 * المعالج المحلي البديل - يعمل عند فشل الذكاء الاصطناعي
 * يستخدم تحليل محلي بالجافاسكريبت
 */
function fallbackProcessor() {
  try {
    Logger.log('=== بدء المعالجة المحلية البديلة ===');
    
    // TODO: سيتم تطوير هذه الدالة في مرحلة لاحقة
    // 1. قراءة البيانات الفاشلة
    // 2. قراءة البرومبت من Google Docs
    // 3. التحليل المحلي بالجافاسكريبت
    // 4. تحديث البيانات عند النجاح
    // 5. إرسال إشعار النجاح أو الفشل النهائي
    
    Logger.log('المعالج المحلي البديل - في انتظار التطوير');
    
  } catch (error) {
    Logger.log('خطأ في fallbackProcessor: ' + error.toString());
  }
}

/**
 * دالة اختبار لتجربة النظام
 * يمكن استدعاؤها يدوياً لاختبار المكونات
 */
function testSystem() {
  try {
    Logger.log('=== بدء اختبار النظام ===');
    
    // اختبار الدوال المساعدة
    if (typeof generateUnitCode === 'function') {
      Logger.log('✓ دالة generateUnitCode متوفرة');
    } else {
      Logger.log('✗ دالة generateUnitCode غير متوفرة');
    }
    
    if (typeof sendOrUpdateNotification === 'function') {
      Logger.log('✓ دالة sendOrUpdateNotification متوفرة');
    } else {
      Logger.log('✗ دالة sendOrUpdateNotification غير متوفرة');
    }
    
    // اختبار الإعدادات
    const properties = PropertiesService.getScriptProperties();
    const sheetId = properties.getProperty('SHEET_ID');
    
    if (sheetId) {
      Logger.log('✓ إعدادات النظام متوفرة');
    } else {
      Logger.log('✗ إعدادات النظام غير مكتملة');
    }
    
    Logger.log('=== انتهاء اختبار النظام ===');
    
  } catch (error) {
    Logger.log('خطأ في اختبار النظام: ' + error.toString());
  }
}

/**
 * قراءة بيانات النموذج من الصف
 *
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet - ورقة العمل
 * @param {number} rowNum - رقم الصف
 * @param {Object} headers - خريطة العناوين
 * @returns {Object} بيانات النموذج
 */
function getFormDataFromRow(sheet, rowNum, headers) {
  try {
    Logger.log(`قراءة بيانات النموذج من الصف ${rowNum}`);

    const rowData = sheet.getRange(rowNum, 1, 1, sheet.getLastColumn()).getValues()[0];
    const formData = {};

    // تحويل البيانات إلى كائن
    for (const [headerName, colIndex] of Object.entries(headers)) {
      formData[headerName] = rowData[colIndex - 1] || '';
    }

    Logger.log('تم قراءة بيانات النموذج بنجاح');
    return formData;

  } catch (error) {
    Logger.log('خطأ في قراءة بيانات النموذج: ' + error.toString());
    return {};
  }
}

/**
 * معالجة البيانات المتعددة
 *
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet - ورقة العمل
 * @param {number} originalRow - الصف الأصلي
 * @param {Object} headers - خريطة العناوين
 * @param {Object} formData - بيانات النموذج
 */
function processMultipleStatements(sheet, originalRow, headers, formData) {
  try {
    Logger.log('معالجة البيانات المتعددة');

    const properties = PropertiesService.getScriptProperties();
    const multiValueField = properties.getProperty('MULTI_VALUE_FIELD') || '🪀 بيانات متعددة غير مفصله';
    const singleValueField = properties.getProperty('SINGLE_VALUE_FIELD') || '🪀 بيان عقار غير مفصل';

    const multiValueText = formData[multiValueField] || '';

    if (!multiValueText.trim()) {
      Logger.log('لا توجد بيانات متعددة للمعالجة');
      return;
    }

    // تقسيم البيانات المتعددة
    const statements = splitMultiValueField(multiValueText);
    Logger.log(`تم تقسيم البيانات إلى ${statements.length} بيان`);

    if (statements.length <= 1) {
      // بيان واحد فقط - تحديث الصف الحالي
      if (statements.length === 1) {
        const singleValueCol = headers[singleValueField];
        if (singleValueCol) {
          sheet.getRange(originalRow, singleValueCol).setValue(statements[0]);
        }
      }
      return;
    }

    // إنشاء صفوف جديدة للبيانات المتعددة
    for (let i = 0; i < statements.length; i++) {
      let targetRow;

      if (i === 0) {
        // استخدام الصف الأصلي للبيان الأول
        targetRow = originalRow;
      } else {
        // إنشاء صف جديد للبيانات الإضافية
        sheet.insertRowAfter(originalRow + i - 1);
        targetRow = originalRow + i;

        // نسخ البيانات الأساسية من الصف الأصلي
        copyRowData(sheet, originalRow, targetRow, headers);
      }

      // تحديث البيان في العمود المناسب
      const singleValueCol = headers[singleValueField];
      if (singleValueCol) {
        sheet.getRange(targetRow, singleValueCol).setValue(statements[i]);
      }

      // توليد كود وحدة جديد لكل بيان
      const unitCodeCol = headers['كود الوحدة'];
      if (unitCodeCol) {
        const unitCode = generateUnitCode(sheet, unitCodeCol);
        sheet.getRange(targetRow, unitCodeCol).setValue(unitCode);

        // إرسال إشعار لكل بيان
        sendOrUpdateNotification(unitCode, 'تخزين', `بيان ${i + 1} من ${statements.length}`, true);
      }
    }

    Logger.log('تم معالجة البيانات المتعددة بنجاح');

  } catch (error) {
    Logger.log('خطأ في معالجة البيانات المتعددة: ' + error.toString());
  }
}

/**
 * معالجة البيان الواحد غير المفصل
 *
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet - ورقة العمل
 * @param {number} rowNum - رقم الصف
 * @param {Object} headers - خريطة العناوين
 * @param {Object} formData - بيانات النموذج
 * @param {string} unitCode - كود الوحدة
 */
function processSingleStatement(sheet, rowNum, headers, formData, unitCode) {
  try {
    Logger.log(`معالجة البيان الواحد - كود: ${unitCode}`);

    // TODO: سيتم تطوير تحليل البيان في المراحل القادمة
    // هنا سيتم إرسال البيان للذكاء الاصطناعي أو المعالجة المحلية

    Logger.log('البيان جاهز للتحليل في المرحلة القادمة');

  } catch (error) {
    Logger.log('خطأ في معالجة البيان الواحد: ' + error.toString());
  }
}

/**
 * معالجة البيان المفصل
 *
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet - ورقة العمل
 * @param {number} rowNum - رقم الصف
 * @param {Object} headers - خريطة العناوين
 * @param {Object} formData - بيانات النموذج
 * @param {string} unitCode - كود الوحدة
 */
function processDetailedStatement(sheet, rowNum, headers, formData, unitCode) {
  try {
    Logger.log(`معالجة البيان المفصل - كود: ${unitCode}`);

    // فحص العقار للتلف
    const isDamaged = checkAndMarkDamaged(sheet, rowNum, headers, unitCode, headers['Status']);

    if (isDamaged) {
      Logger.log('العقار مصنف كتالف');
      sendOrUpdateNotification(unitCode, 'تالف', 'العقار يحتوي على حقول إلزامية فارغة');
    } else {
      Logger.log('العقار سليم ومكتمل');

      // تحديث حالة النجاح
      updateSuccessStates(sheet, rowNum, headers, 'تفكيك');

      // إرسال إشعار التحديث
      sendOrUpdateNotification(unitCode, 'تفكيك', 'تم تحليل البيان المفصل بنجاح');
    }

  } catch (error) {
    Logger.log('خطأ في معالجة البيان المفصل: ' + error.toString());
  }
}

/**
 * نسخ بيانات من صف إلى آخر
 *
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet - ورقة العمل
 * @param {number} sourceRow - الصف المصدر
 * @param {number} targetRow - الصف الهدف
 * @param {Object} headers - خريطة العناوين
 */
function copyRowData(sheet, sourceRow, targetRow, headers) {
  try {
    Logger.log(`نسخ البيانات من الصف ${sourceRow} إلى الصف ${targetRow}`);

    // الحقول التي يجب نسخها
    const fieldsToCopy = [
      'طابع زمني',
      'اسم الموظف',
      'تاريخ الإنشاء'
    ];

    for (const fieldName of fieldsToCopy) {
      if (headers[fieldName]) {
        const value = sheet.getRange(sourceRow, headers[fieldName]).getValue();
        sheet.getRange(targetRow, headers[fieldName]).setValue(value);
      }
    }

    // تطبيق القيم الافتراضية للصف الجديد
    setDefaultValues(sheet, targetRow, headers);

    Logger.log('تم نسخ البيانات بنجاح');

  } catch (error) {
    Logger.log('خطأ في نسخ البيانات: ' + error.toString());
  }
}

/**
 * دالة إعداد النظام الأولي
 * تقوم بتكوين الإعدادات الأساسية
 */
function setupSystem() {
  try {
    Logger.log('=== بدء إعداد النظام ===');

    // 1. التحقق من الإعدادات الأساسية
    const properties = PropertiesService.getScriptProperties();
    const requiredProperties = [
      'SHEET_ID',
      'SHEET_NAME',
      'NOTIFICATIONS_LOG_SHEET_ID'
    ];

    for (const prop of requiredProperties) {
      if (!properties.getProperty(prop)) {
        Logger.log(`⚠️ الإعداد المطلوب مفقود: ${prop}`);
      } else {
        Logger.log(`✓ الإعداد موجود: ${prop}`);
      }
    }

    // 2. إنشاء شيت سجل الإشعارات
    const logCreated = createNotificationLogSheet();
    if (logCreated) {
      Logger.log('✓ شيت سجل الإشعارات جاهز');
    } else {
      Logger.log('⚠️ مشكلة في شيت سجل الإشعارات');
    }

    // 3. اختبار الوصول للشيت الرئيسي
    try {
      const sheetId = getMainSheetId();
      const sheetName = getMainSheetName();

      if (sheetId && sheetName) {
        const spreadsheet = SpreadsheetApp.openById(sheetId);
        const sheet = spreadsheet.getSheetByName(sheetName);

        if (sheet) {
          Logger.log('✓ الوصول للشيت الرئيسي ناجح');

          // قراءة العناوين
          const headers = getHeadersMap(sheet);
          Logger.log(`✓ تم قراءة ${Object.keys(headers).length} عنوان`);
        } else {
          Logger.log(`⚠️ ورقة العمل ${sheetName} غير موجودة`);
        }
      }
    } catch (sheetError) {
      Logger.log('⚠️ خطأ في الوصول للشيت الرئيسي: ' + sheetError.toString());
    }

    Logger.log('=== انتهاء إعداد النظام ===');

  } catch (error) {
    Logger.log('خطأ في إعداد النظام: ' + error.toString());
  }
}

// ===================================================================
// نهاية ملف main.js - المرحلة الأولى
// ===================================================================
