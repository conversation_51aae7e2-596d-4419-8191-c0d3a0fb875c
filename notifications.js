/**
 * ===================================================================
 * AQAR BOT - NOTIFICATIONS SYSTEM
 * ===================================================================
 * 
 * ملف نظام الإشعارات
 * يحتوي على جميع دوال إرسال وإدارة الإشعارات عبر Google Chat
 * 
 * الوظائف الرئيسية:
 * - sendOrUpdateNotification: إرسال أو تحديث الإشعارات
 * - buildChatCardPayload: بناء محتوى رسائل Google Chat
 * - manageNotificationLog: إدارة سجل الإشعارات
 * 
 * <AUTHOR> Bot Team
 * @version 1.0.0 - المرحلة الأولى
 * @created 2025-01-31
 * ===================================================================
 */

/**
 * إرسال أو تحديث إشعار في Google Chat
 * 
 * @param {string} unitCode - كود الوحدة
 * @param {string} status - حالة العقار
 * @param {string} details - تفاصيل إضافية
 * @param {boolean} isInitial - هل هو إشعار أولي
 * @returns {boolean} نجح الإرسال أم لا
 */
function sendOrUpdateNotification(unitCode, status, details, isInitial = false) {
  try {
    Logger.log(`بدء إرسال/تحديث إشعار - كود الوحدة: ${unitCode}, الحالة: ${status}`);
    
    // البحث عن الإشعار الموجود في سجل الإشعارات
    const existingNotification = findNotificationInLog(unitCode);
    
    // بناء محتوى الرسالة
    const payload = buildChatCardPayload(unitCode, status, details, isInitial);
    
    let messageId = null;
    let success = false;
    
    if (existingNotification && !isInitial) {
      // تحديث الرسالة الموجودة
      Logger.log(`تحديث الرسالة الموجودة - messageId: ${existingNotification.messageId}`);
      success = updateChatMessage(existingNotification.messageId, payload);
      messageId = existingNotification.messageId;
    } else {
      // إرسال رسالة جديدة
      Logger.log('إرسال رسالة جديدة');
      const response = sendNewChatMessage(payload, unitCode);
      success = response.success;
      messageId = response.messageId;
    }
    
    if (success && messageId) {
      // تحديث سجل الإشعارات
      updateNotificationLog(unitCode, messageId, status);
      Logger.log('تم إرسال/تحديث الإشعار بنجاح');
      return true;
    } else {
      Logger.log('فشل في إرسال/تحديث الإشعار');
      return false;
    }
    
  } catch (error) {
    Logger.log('خطأ في إرسال/تحديث الإشعار: ' + error.toString());
    return false;
  }
}

/**
 * بناء محتوى رسالة Google Chat
 * 
 * @param {string} unitCode - كود الوحدة
 * @param {string} status - حالة العقار
 * @param {string} details - التفاصيل
 * @param {boolean} isInitial - هل هو إشعار أولي
 * @returns {Object} محتوى الرسالة
 */
function buildChatCardPayload(unitCode, status, details, isInitial) {
  try {
    Logger.log(`بناء محتوى الرسالة - كود: ${unitCode}, حالة: ${status}`);
    
    let title = '';
    let subtitle = '';
    let color = '';
    let icon = '';
    
    // تحديد محتوى الرسالة حسب الحالة
    switch (status) {
      case 'تخزين':
        title = '📥 تم استقبال عقار جديد';
        subtitle = `كود الوحدة: ${unitCode}`;
        color = '#4285F4'; // أزرق
        icon = '📥';
        break;
        
      case 'تفكيك':
        title = '🔍 تم تحليل العقار بنجاح';
        subtitle = `كود الوحدة: ${unitCode}`;
        color = '#34A853'; // أخضر
        icon = '🔍';
        break;
        
      case 'نجاح':
        title = '✅ تم تخزين عقار جديد بنجاح';
        subtitle = `كود الوحدة: ${unitCode}`;
        color = '#34A853'; // أخضر
        icon = '✅';
        break;
        
      case 'فشل':
        title = '❌ عقار فاشل';
        subtitle = `كود الوحدة: ${unitCode}`;
        color = '#EA4335'; // أحمر
        icon = '❌';
        break;
        
      case 'تالف':
        title = '🗑️ عقار تالف';
        subtitle = `كود الوحدة: ${unitCode} - حقول إلزامية مفقودة`;
        color = '#FBBC04'; // أصفر
        icon = '🗑️';
        break;
        
      default:
        title = '📋 تحديث حالة العقار';
        subtitle = `كود الوحدة: ${unitCode}`;
        color = '#9AA0A6'; // رمادي
        icon = '📋';
    }
    
    // بناء البطاقة
    const card = {
      cards: [{
        header: {
          title: title,
          subtitle: subtitle,
          imageUrl: '', // يمكن إضافة صورة لاحقاً
          imageStyle: 'IMAGE'
        },
        sections: [{
          widgets: [
            {
              textParagraph: {
                text: `${icon} <b>كود الوحدة:</b> ${unitCode}<br>` +
                      `📅 <b>التاريخ:</b> ${new Date().toLocaleString('ar-EG')}<br>` +
                      `📊 <b>الحالة:</b> ${status}`
              }
            }
          ]
        }]
      }]
    };
    
    // إضافة التفاصيل إذا كانت متوفرة
    if (details && details.trim()) {
      card.cards[0].sections[0].widgets.push({
        textParagraph: {
          text: `📝 <b>التفاصيل:</b><br>${details}`
        }
      });
    }
    
    // إضافة أزرار للإشعارات المتقدمة
    if (status === 'فشل') {
      card.cards[0].sections.push({
        widgets: [{
          buttons: [{
            textButton: {
              text: '🔄 إعادة المحاولة',
              onClick: {
                action: {
                  actionMethodName: 'retryProcessing',
                  parameters: [{
                    key: 'unitCode',
                    value: unitCode
                  }]
                }
              }
            }
          }]
        }]
      });
    }
    
    Logger.log('تم بناء محتوى الرسالة بنجاح');
    return card;
    
  } catch (error) {
    Logger.log('خطأ في بناء محتوى الرسالة: ' + error.toString());
    
    // رسالة بسيطة في حالة الخطأ
    return {
      text: `${unitCode} - ${status}: ${details || 'تحديث حالة العقار'}`
    };
  }
}

/**
 * إرسال رسالة جديدة إلى Google Chat
 * 
 * @param {Object} payload - محتوى الرسالة
 * @param {string} threadKey - مفتاح المحادثة (كود الوحدة)
 * @returns {Object} نتيجة الإرسال {success: boolean, messageId: string}
 */
function sendNewChatMessage(payload, threadKey) {
  try {
    Logger.log(`إرسال رسالة جديدة - threadKey: ${threadKey}`);
    
    // TODO: سيتم تطوير هذه الدالة لاحقاً للاتصال الفعلي بـ Google Chat API
    // const properties = PropertiesService.getScriptProperties();
    // const chatSpaceId = properties.getProperty('CHAT_SPACE_ID');
    // const chatApiUrl = `https://chat.googleapis.com/v1/spaces/${chatSpaceId}/messages`;
    
    // محاكاة الإرسال للمرحلة الأولى
    Logger.log('محاكاة إرسال رسالة جديدة - سيتم التطوير الفعلي لاحقاً');
    
    // إنشاء messageId وهمي للاختبار
    const mockMessageId = `msg_${threadKey}_${Date.now()}`;
    
    return {
      success: true,
      messageId: mockMessageId
    };
    
  } catch (error) {
    Logger.log('خطأ في إرسال رسالة جديدة: ' + error.toString());
    return {
      success: false,
      messageId: null
    };
  }
}

/**
 * تحديث رسالة موجودة في Google Chat
 * 
 * @param {string} messageId - معرف الرسالة
 * @param {Object} payload - المحتوى الجديد
 * @returns {boolean} نجح التحديث أم لا
 */
function updateChatMessage(messageId, payload) {
  try {
    Logger.log(`تحديث رسالة موجودة - messageId: ${messageId}`);
    
    // TODO: سيتم تطوير هذه الدالة لاحقاً للاتصال الفعلي بـ Google Chat API
    // const properties = PropertiesService.getScriptProperties();
    // const chatApiUrl = `https://chat.googleapis.com/v1/${messageId}`;
    
    // محاكاة التحديث للمرحلة الأولى
    Logger.log('محاكاة تحديث الرسالة - سيتم التطوير الفعلي لاحقاً');
    
    return true;
    
  } catch (error) {
    Logger.log('خطأ في تحديث الرسالة: ' + error.toString());
    return false;
  }
}

/**
 * البحث عن إشعار في سجل الإشعارات
 * 
 * @param {string} unitCode - كود الوحدة
 * @returns {Object|null} بيانات الإشعار أو null
 */
function findNotificationInLog(unitCode) {
  try {
    Logger.log(`البحث عن إشعار في السجل - كود الوحدة: ${unitCode}`);
    
    const properties = PropertiesService.getScriptProperties();
    const logSheetId = properties.getProperty('NOTIFICATIONS_LOG_SHEET_ID');
    
    if (!logSheetId) {
      Logger.log('معرف شيت سجل الإشعارات غير موجود');
      return null;
    }
    
    const logSheet = SpreadsheetApp.openById(logSheetId).getActiveSheet();
    const lastRow = logSheet.getLastRow();
    
    if (lastRow < 2) {
      Logger.log('سجل الإشعارات فارغ');
      return null;
    }
    
    // البحث في السجل
    const data = logSheet.getRange(2, 1, lastRow - 1, 4).getValues();
    
    for (let i = 0; i < data.length; i++) {
      if (data[i][0] === unitCode) {
        Logger.log(`تم العثور على إشعار موجود - messageId: ${data[i][1]}`);
        return {
          unitCode: data[i][0],
          messageId: data[i][1],
          lastUpdated: data[i][2],
          status: data[i][3]
        };
      }
    }
    
    Logger.log('لم يتم العثور على إشعار موجود');
    return null;
    
  } catch (error) {
    Logger.log('خطأ في البحث عن الإشعار: ' + error.toString());
    return null;
  }
}

/**
 * تحديث سجل الإشعارات
 *
 * @param {string} unitCode - كود الوحدة
 * @param {string} messageId - معرف الرسالة
 * @param {string} status - الحالة
 */
function updateNotificationLog(unitCode, messageId, status) {
  try {
    Logger.log(`تحديث سجل الإشعارات - كود: ${unitCode}, messageId: ${messageId}`);

    const properties = PropertiesService.getScriptProperties();
    const logSheetId = properties.getProperty('NOTIFICATIONS_LOG_SHEET_ID');

    if (!logSheetId) {
      Logger.log('معرف شيت سجل الإشعارات غير موجود');
      return;
    }

    const logSheet = SpreadsheetApp.openById(logSheetId).getActiveSheet();

    // البحث عن السجل الموجود
    const existingRecord = findNotificationInLog(unitCode);

    if (existingRecord) {
      // تحديث السجل الموجود
      const lastRow = logSheet.getLastRow();
      const data = logSheet.getRange(2, 1, lastRow - 1, 4).getValues();

      for (let i = 0; i < data.length; i++) {
        if (data[i][0] === unitCode) {
          const rowNum = i + 2;
          logSheet.getRange(rowNum, 3).setValue(new Date()); // LastUpdated
          logSheet.getRange(rowNum, 4).setValue(status); // Status
          Logger.log('تم تحديث السجل الموجود');
          return;
        }
      }
    } else {
      // إضافة سجل جديد
      const newRow = logSheet.getLastRow() + 1;
      logSheet.getRange(newRow, 1, 1, 4).setValues([[
        unitCode,
        messageId,
        new Date(),
        status
      ]]);
      Logger.log('تم إضافة سجل جديد');
    }

  } catch (error) {
    Logger.log('خطأ في تحديث سجل الإشعارات: ' + error.toString());
  }
}

/**
 * إنشاء شيت سجل الإشعارات إذا لم يكن موجوداً
 */
function createNotificationLogSheet() {
  try {
    Logger.log('إنشاء شيت سجل الإشعارات');

    const properties = PropertiesService.getScriptProperties();
    const logSheetId = properties.getProperty('NOTIFICATIONS_LOG_SHEET_ID');

    if (!logSheetId) {
      Logger.log('معرف شيت سجل الإشعارات غير موجود في الإعدادات');
      return false;
    }

    try {
      const logSheet = SpreadsheetApp.openById(logSheetId).getActiveSheet();

      // التحقق من وجود العناوين
      const headers = logSheet.getRange(1, 1, 1, 4).getValues()[0];

      if (!headers[0] || headers[0] !== 'UnitCode') {
        // إضافة العناوين
        logSheet.getRange(1, 1, 1, 4).setValues([
          ['UnitCode', 'MessageId', 'LastUpdated', 'Status']
        ]);

        // تنسيق العناوين
        const headerRange = logSheet.getRange(1, 1, 1, 4);
        headerRange.setFontWeight('bold');
        headerRange.setBackground('#4285F4');
        headerRange.setFontColor('white');

        Logger.log('تم إنشاء عناوين شيت سجل الإشعارات');
      }

      return true;

    } catch (sheetError) {
      Logger.log('خطأ في الوصول لشيت سجل الإشعارات: ' + sheetError.toString());
      return false;
    }

  } catch (error) {
    Logger.log('خطأ في إنشاء شيت سجل الإشعارات: ' + error.toString());
    return false;
  }
}

/**
 * بناء رسالة إشعار مفصلة للنجاح
 *
 * @param {string} unitCode - كود الوحدة
 * @param {Object} propertyData - بيانات العقار
 * @param {Object} links - الروابط المختلفة
 * @returns {Object} محتوى الرسالة المفصلة
 */
function buildSuccessNotification(unitCode, propertyData, links = {}) {
  try {
    Logger.log(`بناء إشعار النجاح المفصل - كود: ${unitCode}`);

    // بناء النص المفصل
    let detailsText = `✅ <b>تم تخزين عقار جديد بنجاح</b>\n\n`;

    // معلومات العقار الأساسية
    detailsText += `🏠 <b>كود الوحدة:</b> ${unitCode}\n`;

    if (propertyData.ownerName && propertyData.ownerName !== 'غير محدد') {
      detailsText += `👤 <b>اسم المالك:</b> ${propertyData.ownerName}\n`;
    }

    if (propertyData.ownerPhone && propertyData.ownerPhone !== '01000000000') {
      detailsText += `📞 <b>رقم المالك:</b> ${propertyData.ownerPhone}\n`;
    }

    if (propertyData.area && propertyData.area !== 'غير محدد') {
      detailsText += `📍 <b>المنطقة:</b> ${propertyData.area}\n`;
    }

    if (propertyData.unitType && propertyData.unitType !== 'غير محدد') {
      detailsText += `🏢 <b>نوع الوحدة:</b> ${propertyData.unitType}\n`;
    }

    if (propertyData.unitStatus && propertyData.unitStatus !== 'غير محدد') {
      detailsText += `🔧 <b>حالة الوحدة:</b> ${propertyData.unitStatus}\n`;
    }

    if (propertyData.availability && propertyData.availability !== 'غير محدد') {
      detailsText += `📋 <b>إتاحة العقار:</b> ${propertyData.availability}\n`;
    }

    if (propertyData.imageStatus && propertyData.imageStatus !== 'غير محدد') {
      detailsText += `📸 <b>حالة الصور:</b> ${propertyData.imageStatus}\n`;
    }

    if (propertyData.price && propertyData.price > 0) {
      detailsText += `💰 <b>السعر:</b> ${propertyData.price.toLocaleString()} جنيه\n`;
    }

    if (propertyData.size && propertyData.size > 0) {
      detailsText += `📐 <b>المساحة:</b> ${propertyData.size} متر مربع\n`;
    }

    // إضافة الروابط
    detailsText += `\n🔗 <b>الروابط:</b>\n`;

    if (links.sheetLink) {
      detailsText += `📊 <a href="${links.sheetLink}">رابط Google Sheet</a>\n`;
    }

    if (links.notionLink) {
      detailsText += `📝 <a href="${links.notionLink}">رابط صفحة Notion</a>\n`;
    }

    if (links.zohoLink) {
      detailsText += `🗂️ <a href="${links.zohoLink}">رابط سجل Zoho</a>\n`;
    }

    // إضافة البيان الكامل إذا كان متوفراً
    if (propertyData.fullStatement && propertyData.fullStatement.trim()) {
      detailsText += `\n📄 <b>البيان الكامل:</b>\n${propertyData.fullStatement}`;
    }

    // بناء البطاقة
    const card = {
      cards: [{
        header: {
          title: '✅ عقار ناجح',
          subtitle: `كود الوحدة: ${unitCode}`,
          imageStyle: 'IMAGE'
        },
        sections: [{
          widgets: [{
            textParagraph: {
              text: detailsText
            }
          }]
        }]
      }]
    };

    Logger.log('تم بناء إشعار النجاح المفصل');
    return card;

  } catch (error) {
    Logger.log('خطأ في بناء إشعار النجاح: ' + error.toString());

    // رسالة بسيطة في حالة الخطأ
    return {
      text: `✅ تم تخزين العقار ${unitCode} بنجاح`
    };
  }
}

/**
 * بناء رسالة إشعار الفشل
 *
 * @param {string} unitCode - كود الوحدة
 * @param {string} failureReason - سبب الفشل
 * @param {Array} completedSteps - الخطوات المكتملة
 * @param {string} rawStatement - البيان الخام
 * @returns {Object} محتوى رسالة الفشل
 */
function buildFailureNotification(unitCode, failureReason, completedSteps = [], rawStatement = '') {
  try {
    Logger.log(`بناء إشعار الفشل - كود: ${unitCode}`);

    let detailsText = `❌ <b>عقار فاشل</b>\n\n`;

    detailsText += `🏠 <b>كود الوحدة:</b> ${unitCode}\n`;
    detailsText += `⏰ <b>وقت الفشل:</b> ${new Date().toLocaleString('ar-EG')}\n`;

    if (failureReason) {
      detailsText += `🚫 <b>سبب الفشل:</b> ${failureReason}\n`;
    }

    // عرض المراحل المكتملة والفاشلة
    detailsText += `\n📊 <b>حالة المراحل:</b>\n`;

    const allSteps = ['تخزين', 'تفكيك', 'نوشن', 'زوهو', 'اشعار'];

    for (const step of allSteps) {
      if (completedSteps.includes(step)) {
        detailsText += `✔️ ${step}\n`;
      } else {
        detailsText += `✖️ ${step}\n`;
      }
    }

    // إضافة البيان الخام
    if (rawStatement && rawStatement.trim()) {
      detailsText += `\n📄 <b>البيان الخام:</b>\n${rawStatement}`;
    }

    // بناء البطاقة
    const card = {
      cards: [{
        header: {
          title: '❌ عقار فاشل',
          subtitle: `كود الوحدة: ${unitCode}`,
          imageStyle: 'IMAGE'
        },
        sections: [{
          widgets: [{
            textParagraph: {
              text: detailsText
            }
          }]
        }, {
          widgets: [{
            buttons: [{
              textButton: {
                text: '🔄 إعادة المحاولة',
                onClick: {
                  action: {
                    actionMethodName: 'retryProcessing',
                    parameters: [{
                      key: 'unitCode',
                      value: unitCode
                    }]
                  }
                }
              }
            }]
          }]
        }]
      }]
    };

    Logger.log('تم بناء إشعار الفشل');
    return card;

  } catch (error) {
    Logger.log('خطأ في بناء إشعار الفشل: ' + error.toString());

    // رسالة بسيطة في حالة الخطأ
    return {
      text: `❌ فشل في معالجة العقار ${unitCode}: ${failureReason || 'خطأ غير محدد'}`
    };
  }
}

// ===================================================================
// نهاية ملف notifications.js - المرحلة الأولى
// ===================================================================
