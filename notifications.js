/**
 * ===================================================================
 * AQAR BOT - NOTIFICATIONS SYSTEM
 * ===================================================================
 *
 * ملف نظام الإشعارات
 * يحتوي على جميع دوال إرسال وإدارة الإشعارات عبر Telegram و Discord
 *
 * الوظائف الرئيسية:
 * - sendOrUpdateNotification: إرسال أو تحديث الإشعارات
 * - sendTelegramNotification: إرسال إشعارات Telegram
 * - sendDiscordNotification: إرسال إشعارات Discord
 * - manageNotificationLog: إدارة سجل الإشعارات
 *
 * <AUTHOR> Bot Team
 * @version 1.0.0 - المرحلة الأولى (محدث للدعم Telegram & Discord)
 * @created 2025-01-31
 * ===================================================================
 */

/**
 * إرسال أو تحديث إشعار عبر Telegram و Discord
 *
 * @param {string} unitCode - كود الوحدة
 * @param {string} status - حالة العقار
 * @param {string} details - تفاصيل إضافية
 * @param {boolean} isInitial - هل هو إشعار أولي
 * @returns {boolean} نجح الإرسال أم لا
 */
function sendOrUpdateNotification(unitCode, status, details, isInitial = false) {
  try {
    Logger.log(`بدء إرسال/تحديث إشعار - كود الوحدة: ${unitCode}, الحالة: ${status}`);

    // البحث عن الإشعار الموجود في سجل الإشعارات
    const existingNotification = findNotificationInLog(unitCode);

    let telegramSuccess = false;
    let discordSuccess = false;
    let messageIds = {};

    if (existingNotification && !isInitial) {
      // تحديث الرسائل الموجودة
      Logger.log(`تحديث الرسائل الموجودة - Telegram: ${existingNotification.telegramMessageId}, Discord: ${existingNotification.discordMessageId}`);

      if (existingNotification.telegramMessageId) {
        telegramSuccess = updateTelegramMessage(existingNotification.telegramMessageId, unitCode, status, details);
        messageIds.telegram = existingNotification.telegramMessageId;
      }

      if (existingNotification.discordMessageId) {
        discordSuccess = updateDiscordMessage(existingNotification.discordMessageId, unitCode, status, details);
        messageIds.discord = existingNotification.discordMessageId;
      }

    } else {
      // إرسال رسائل جديدة
      Logger.log('إرسال رسائل جديدة');

      // إرسال إلى Telegram
      const telegramResponse = sendTelegramNotification(unitCode, status, details, isInitial);
      telegramSuccess = telegramResponse.success;
      if (telegramSuccess) {
        messageIds.telegram = telegramResponse.messageId;
      }

      // إرسال إلى Discord
      const discordResponse = sendDiscordNotification(unitCode, status, details, isInitial);
      discordSuccess = discordResponse.success;
      if (discordSuccess) {
        messageIds.discord = discordResponse.messageId;
      }
    }

    // تحديث سجل الإشعارات إذا نجح أي من الإرسالين
    if (telegramSuccess || discordSuccess) {
      updateNotificationLog(unitCode, messageIds, status);
      Logger.log(`تم إرسال/تحديث الإشعار - Telegram: ${telegramSuccess ? '✅' : '❌'}, Discord: ${discordSuccess ? '✅' : '❌'}`);
      return true;
    } else {
      Logger.log('فشل في إرسال/تحديث الإشعار في جميع المنصات');
      return false;
    }

  } catch (error) {
    Logger.log('خطأ في إرسال/تحديث الإشعار: ' + error.toString());
    return false;
  }
}

/**
 * إرسال إشعار جديد عبر Telegram
 *
 * @param {string} unitCode - كود الوحدة
 * @param {string} status - حالة العقار
 * @param {string} details - التفاصيل
 * @param {boolean} isInitial - هل هو إشعار أولي
 * @returns {Object} نتيجة الإرسال {success: boolean, messageId: string}
 */
function sendTelegramNotification(unitCode, status, details, isInitial = false) {
  try {
    Logger.log(`إرسال إشعار Telegram - كود: ${unitCode}, حالة: ${status}`);

    const properties = PropertiesService.getScriptProperties();
    const botToken = properties.getProperty('TELEGRAM_CONTROLER_BOT_TOKEN');
    const chatId = properties.getProperty('TELEGRAM_ADMIN_CHAT_ID');

    if (!botToken || !chatId) {
      Logger.log('إعدادات Telegram غير مكتملة');
      return { success: false, messageId: null };
    }

    // بناء نص الرسالة
    const messageText = buildTelegramMessage(unitCode, status, details, isInitial);

    // إعداد البيانات للإرسال
    const payload = {
      chat_id: chatId,
      text: messageText,
      parse_mode: 'HTML',
      disable_web_page_preview: true
    };

    // إرسال الرسالة
    const url = `https://api.telegram.org/bot${botToken}/sendMessage`;
    const options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      payload: JSON.stringify(payload)
    };

    const response = UrlFetchApp.fetch(url, options);
    const responseData = JSON.parse(response.getContentText());

    if (responseData.ok) {
      Logger.log(`تم إرسال رسالة Telegram بنجاح - Message ID: ${responseData.result.message_id}`);
      return {
        success: true,
        messageId: responseData.result.message_id.toString()
      };
    } else {
      Logger.log(`فشل إرسال رسالة Telegram: ${responseData.description}`);
      return { success: false, messageId: null };
    }

  } catch (error) {
    Logger.log('خطأ في إرسال إشعار Telegram: ' + error.toString());
    return { success: false, messageId: null };
  }
}

/**
 * إرسال إشعار جديد عبر Discord
 *
 * @param {string} unitCode - كود الوحدة
 * @param {string} status - حالة العقار
 * @param {string} details - التفاصيل
 * @param {boolean} isInitial - هل هو إشعار أولي
 * @returns {Object} نتيجة الإرسال {success: boolean, messageId: string}
 */
function sendDiscordNotification(unitCode, status, details, isInitial = false) {
  try {
    Logger.log(`إرسال إشعار Discord - كود: ${unitCode}, حالة: ${status}`);

    const properties = PropertiesService.getScriptProperties();
    const webhookUrl = properties.getProperty('DISCORD_WEBHOOK_URL');

    if (!webhookUrl) {
      Logger.log('رابط Discord Webhook غير موجود');
      return { success: false, messageId: null };
    }

    // بناء محتوى الرسالة
    const embed = buildDiscordEmbed(unitCode, status, details, isInitial);

    // إعداد البيانات للإرسال
    const payload = {
      embeds: [embed],
      username: 'Aqar Bot',
      avatar_url: '' // يمكن إضافة صورة للبوت
    };

    // إرسال الرسالة
    const options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      payload: JSON.stringify(payload)
    };

    const response = UrlFetchApp.fetch(webhookUrl, options);

    if (response.getResponseCode() === 204) {
      // Discord لا يرجع message ID مع الـ webhook، لذا سنستخدم timestamp
      const messageId = `discord_${Date.now()}`;
      Logger.log(`تم إرسال رسالة Discord بنجاح - Message ID: ${messageId}`);
      return {
        success: true,
        messageId: messageId
      };
    } else {
      Logger.log(`فشل إرسال رسالة Discord: ${response.getResponseCode()}`);
      return { success: false, messageId: null };
    }

  } catch (error) {
    Logger.log('خطأ في إرسال إشعار Discord: ' + error.toString());
    return { success: false, messageId: null };
  }
}

/**
 * بناء نص رسالة Telegram
 *
 * @param {string} unitCode - كود الوحدة
 * @param {string} status - حالة العقار
 * @param {string} details - التفاصيل
 * @param {boolean} isInitial - هل هو إشعار أولي
 * @returns {string} نص الرسالة
 */
function buildTelegramMessage(unitCode, status, details, isInitial = false) {
  try {
    let icon = '';
    let title = '';

    // تحديد الأيقونة والعنوان حسب الحالة
    switch (status) {
      case 'تخزين':
        icon = '📥';
        title = 'تم استقبال عقار جديد';
        break;
      case 'تفكيك':
        icon = '🔍';
        title = 'تم تحليل العقار بنجاح';
        break;
      case 'نجاح':
        icon = '✅';
        title = 'تم تخزين عقار جديد بنجاح';
        break;
      case 'فشل':
        icon = '❌';
        title = 'عقار فاشل';
        break;
      case 'تالف':
        icon = '🗑️';
        title = 'عقار تالف';
        break;
      default:
        icon = '📋';
        title = 'تحديث حالة العقار';
    }

    // بناء النص
    let message = `${icon} <b>${title}</b>\n\n`;
    message += `🏠 <b>كود الوحدة:</b> <code>${unitCode}</code>\n`;
    message += `📅 <b>التاريخ:</b> ${new Date().toLocaleString('ar-EG')}\n`;
    message += `📊 <b>الحالة:</b> ${status}\n`;

    if (details && details.trim()) {
      message += `\n📝 <b>التفاصيل:</b>\n${details}`;
    }

    // إضافة هاشتاج للتصنيف
    message += `\n\n#${unitCode.replace(/\./g, '_').replace(/-/g, '_')} #${status}`;

    return message;

  } catch (error) {
    Logger.log('خطأ في بناء رسالة Telegram: ' + error.toString());
    return `${unitCode} - ${status}: ${details || 'تحديث حالة العقار'}`;
  }
}

/**
 * بناء Embed لرسالة Discord
 *
 * @param {string} unitCode - كود الوحدة
 * @param {string} status - حالة العقار
 * @param {string} details - التفاصيل
 * @param {boolean} isInitial - هل هو إشعار أولي
 * @returns {Object} كائن Discord Embed
 */
function buildDiscordEmbed(unitCode, status, details, isInitial = false) {
  try {
    let color = 0x3498db; // أزرق افتراضي
    let title = '';
    let icon = '';

    // تحديد اللون والعنوان حسب الحالة
    switch (status) {
      case 'تخزين':
        color = 0x3498db; // أزرق
        icon = '📥';
        title = 'تم استقبال عقار جديد';
        break;
      case 'تفكيك':
        color = 0x2ecc71; // أخضر
        icon = '🔍';
        title = 'تم تحليل العقار بنجاح';
        break;
      case 'نجاح':
        color = 0x27ae60; // أخضر داكن
        icon = '✅';
        title = 'تم تخزين عقار جديد بنجاح';
        break;
      case 'فشل':
        color = 0xe74c3c; // أحمر
        icon = '❌';
        title = 'عقار فاشل';
        break;
      case 'تالف':
        color = 0xf39c12; // أصفر
        icon = '🗑️';
        title = 'عقار تالف';
        break;
      default:
        color = 0x95a5a6; // رمادي
        icon = '📋';
        title = 'تحديث حالة العقار';
    }

    // بناء الـ embed
    const embed = {
      title: `${icon} ${title}`,
      color: color,
      fields: [
        {
          name: '🏠 كود الوحدة',
          value: `\`${unitCode}\``,
          inline: true
        },
        {
          name: '📊 الحالة',
          value: status,
          inline: true
        },
        {
          name: '📅 التاريخ',
          value: new Date().toLocaleString('ar-EG'),
          inline: false
        }
      ],
      timestamp: new Date().toISOString(),
      footer: {
        text: 'Aqar Bot System'
      }
    };

    // إضافة التفاصيل إذا كانت متوفرة
    if (details && details.trim()) {
      embed.fields.push({
        name: '📝 التفاصيل',
        value: details.length > 1024 ? details.substring(0, 1021) + '...' : details,
        inline: false
      });
    }

    return embed;

  } catch (error) {
    Logger.log('خطأ في بناء Discord Embed: ' + error.toString());

    // embed بسيط في حالة الخطأ
    return {
      title: `${unitCode} - ${status}`,
      description: details || 'تحديث حالة العقار',
      color: 0x95a5a6,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * البحث عن إشعار في سجل الإشعارات
 *
 * @param {string} unitCode - كود الوحدة
 * @returns {Object|null} بيانات الإشعار أو null
 */
function findNotificationInLog(unitCode) {
  try {
    Logger.log(`البحث عن إشعار في السجل - كود الوحدة: ${unitCode}`);

    const properties = PropertiesService.getScriptProperties();
    const logSheetId = properties.getProperty('NOTIFICATIONS_LOG_SHEET_ID');

    if (!logSheetId) {
      Logger.log('معرف شيت سجل الإشعارات غير موجود');
      return null;
    }

    const logSheet = SpreadsheetApp.openById(logSheetId).getActiveSheet();
    const lastRow = logSheet.getLastRow();

    if (lastRow < 2) {
      Logger.log('سجل الإشعارات فارغ');
      return null;
    }

    // البحث في السجل (6 أعمدة: UnitCode, TelegramMessageId, DiscordMessageId, LastUpdated, Status, Notes)
    const data = logSheet.getRange(2, 1, lastRow - 1, 6).getValues();

    for (let i = 0; i < data.length; i++) {
      if (data[i][0] === unitCode) {
        Logger.log(`تم العثور على إشعار موجود - Telegram: ${data[i][1]}, Discord: ${data[i][2]}`);
        return {
          unitCode: data[i][0],
          telegramMessageId: data[i][1],
          discordMessageId: data[i][2],
          lastUpdated: data[i][3],
          status: data[i][4],
          notes: data[i][5]
        };
      }
    }

    Logger.log('لم يتم العثور على إشعار موجود');
    return null;

  } catch (error) {
    Logger.log('خطأ في البحث عن الإشعار: ' + error.toString());
    return null;
  }
}

/**
 * تحديث رسالة Telegram موجودة
 *
 * @param {string} messageId - معرف الرسالة
 * @param {string} unitCode - كود الوحدة
 * @param {string} status - الحالة الجديدة
 * @param {string} details - التفاصيل
 * @returns {boolean} نجح التحديث أم لا
 */
function updateTelegramMessage(messageId, unitCode, status, details) {
  try {
    Logger.log(`تحديث رسالة Telegram - messageId: ${messageId}`);

    const properties = PropertiesService.getScriptProperties();
    const botToken = properties.getProperty('TELEGRAM_CONTROLER_BOT_TOKEN');
    const chatId = properties.getProperty('TELEGRAM_ADMIN_CHAT_ID');

    if (!botToken || !chatId) {
      Logger.log('إعدادات Telegram غير مكتملة');
      return false;
    }

    // بناء النص المحدث
    const messageText = buildTelegramMessage(unitCode, status, details, false);

    // إعداد البيانات للتحديث
    const payload = {
      chat_id: chatId,
      message_id: parseInt(messageId),
      text: messageText,
      parse_mode: 'HTML',
      disable_web_page_preview: true
    };

    // تحديث الرسالة
    const url = `https://api.telegram.org/bot${botToken}/editMessageText`;
    const options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      payload: JSON.stringify(payload)
    };

    const response = UrlFetchApp.fetch(url, options);
    const responseData = JSON.parse(response.getContentText());

    if (responseData.ok) {
      Logger.log('تم تحديث رسالة Telegram بنجاح');
      return true;
    } else {
      Logger.log(`فشل تحديث رسالة Telegram: ${responseData.description}`);
      return false;
    }

  } catch (error) {
    Logger.log('خطأ في تحديث رسالة Telegram: ' + error.toString());
    return false;
  }
}

/**
 * تحديث رسالة Discord موجودة
 *
 * @param {string} messageId - معرف الرسالة
 * @param {string} unitCode - كود الوحدة
 * @param {string} status - الحالة الجديدة
 * @param {string} details - التفاصيل
 * @returns {boolean} نجح التحديث أم لا
 */
function updateDiscordMessage(messageId, unitCode, status, details) {
  try {
    Logger.log(`تحديث رسالة Discord - messageId: ${messageId}`);

    // Discord Webhook لا يدعم تحديث الرسائل مباشرة
    // لذا سنرسل رسالة جديدة بدلاً من التحديث
    Logger.log('Discord Webhook لا يدعم التحديث - سيتم إرسال رسالة جديدة');

    const response = sendDiscordNotification(unitCode, status, details, false);
    return response.success;

  } catch (error) {
    Logger.log('خطأ في تحديث رسالة Discord: ' + error.toString());
    return false;
  }
}

/**
 * تحديث سجل الإشعارات
 *
 * @param {string} unitCode - كود الوحدة
 * @param {Object} messageIds - معرفات الرسائل {telegram: string, discord: string}
 * @param {string} status - الحالة
 */
function updateNotificationLog(unitCode, messageIds, status) {
  try {
    Logger.log(`تحديث سجل الإشعارات - كود: ${unitCode}`);

    const properties = PropertiesService.getScriptProperties();
    const logSheetId = properties.getProperty('NOTIFICATIONS_LOG_SHEET_ID');

    if (!logSheetId) {
      Logger.log('معرف شيت سجل الإشعارات غير موجود');
      return;
    }

    const logSheet = SpreadsheetApp.openById(logSheetId).getActiveSheet();

    // البحث عن السجل الموجود
    const existingRecord = findNotificationInLog(unitCode);

    if (existingRecord) {
      // تحديث السجل الموجود
      const lastRow = logSheet.getLastRow();
      const data = logSheet.getRange(2, 1, lastRow - 1, 6).getValues();

      for (let i = 0; i < data.length; i++) {
        if (data[i][0] === unitCode) {
          const rowNum = i + 2;

          // تحديث معرفات الرسائل إذا كانت جديدة
          if (messageIds.telegram) {
            logSheet.getRange(rowNum, 2).setValue(messageIds.telegram); // TelegramMessageId
          }
          if (messageIds.discord) {
            logSheet.getRange(rowNum, 3).setValue(messageIds.discord); // DiscordMessageId
          }

          logSheet.getRange(rowNum, 4).setValue(new Date()); // LastUpdated
          logSheet.getRange(rowNum, 5).setValue(status); // Status

          Logger.log('تم تحديث السجل الموجود');
          return;
        }
      }
    } else {
      // إضافة سجل جديد
      const newRow = logSheet.getLastRow() + 1;
      logSheet.getRange(newRow, 1, 1, 6).setValues([[
        unitCode,
        messageIds.telegram || '',
        messageIds.discord || '',
        new Date(),
        status,
        '' // حقل إضافي للملاحظات
      ]]);
      Logger.log('تم إضافة سجل جديد');
    }

  } catch (error) {
    Logger.log('خطأ في تحديث سجل الإشعارات: ' + error.toString());
  }
}

/**
 * إنشاء شيت سجل الإشعارات إذا لم يكن موجوداً
 */
function createNotificationLogSheet() {
  try {
    Logger.log('إنشاء شيت سجل الإشعارات');

    const properties = PropertiesService.getScriptProperties();
    const logSheetId = properties.getProperty('NOTIFICATIONS_LOG_SHEET_ID');

    if (!logSheetId) {
      Logger.log('معرف شيت سجل الإشعارات غير موجود في الإعدادات');
      return false;
    }

    try {
      const logSheet = SpreadsheetApp.openById(logSheetId).getActiveSheet();

      // التحقق من وجود العناوين الجديدة
      const headers = logSheet.getRange(1, 1, 1, 6).getValues()[0];

      if (!headers[0] || headers[0] !== 'UnitCode' || !headers[1] || headers[1] !== 'TelegramMessageId') {
        // إضافة العناوين الجديدة
        logSheet.getRange(1, 1, 1, 6).setValues([
          ['UnitCode', 'TelegramMessageId', 'DiscordMessageId', 'LastUpdated', 'Status', 'Notes']
        ]);

        // تنسيق العناوين
        const headerRange = logSheet.getRange(1, 1, 1, 6);
        headerRange.setFontWeight('bold');
        headerRange.setBackground('#4285F4');
        headerRange.setFontColor('white');

        // تعديل عرض الأعمدة
        logSheet.setColumnWidth(1, 150); // UnitCode
        logSheet.setColumnWidth(2, 120); // TelegramMessageId
        logSheet.setColumnWidth(3, 120); // DiscordMessageId
        logSheet.setColumnWidth(4, 150); // LastUpdated
        logSheet.setColumnWidth(5, 100); // Status
        logSheet.setColumnWidth(6, 200); // Notes

        Logger.log('تم إنشاء عناوين شيت سجل الإشعارات الجديدة');
      }

      return true;

    } catch (sheetError) {
      Logger.log('خطأ في الوصول لشيت سجل الإشعارات: ' + sheetError.toString());
      return false;
    }

  } catch (error) {
    Logger.log('خطأ في إنشاء شيت سجل الإشعارات: ' + error.toString());
    return false;
  }
}

/**
 * بناء رسالة إشعار مفصلة للنجاح
 *
 * @param {string} unitCode - كود الوحدة
 * @param {Object} propertyData - بيانات العقار
 * @param {Object} links - الروابط المختلفة
 * @returns {Object} محتوى الرسالة المفصلة
 */
function buildSuccessNotification(unitCode, propertyData, links = {}) {
  try {
    Logger.log(`بناء إشعار النجاح المفصل - كود: ${unitCode}`);

    // بناء النص المفصل
    let detailsText = `✅ <b>تم تخزين عقار جديد بنجاح</b>\n\n`;

    // معلومات العقار الأساسية
    detailsText += `🏠 <b>كود الوحدة:</b> ${unitCode}\n`;

    if (propertyData.ownerName && propertyData.ownerName !== 'غير محدد') {
      detailsText += `👤 <b>اسم المالك:</b> ${propertyData.ownerName}\n`;
    }

    if (propertyData.ownerPhone && propertyData.ownerPhone !== '01000000000') {
      detailsText += `📞 <b>رقم المالك:</b> ${propertyData.ownerPhone}\n`;
    }

    if (propertyData.area && propertyData.area !== 'غير محدد') {
      detailsText += `📍 <b>المنطقة:</b> ${propertyData.area}\n`;
    }

    if (propertyData.unitType && propertyData.unitType !== 'غير محدد') {
      detailsText += `🏢 <b>نوع الوحدة:</b> ${propertyData.unitType}\n`;
    }

    if (propertyData.unitStatus && propertyData.unitStatus !== 'غير محدد') {
      detailsText += `🔧 <b>حالة الوحدة:</b> ${propertyData.unitStatus}\n`;
    }

    if (propertyData.availability && propertyData.availability !== 'غير محدد') {
      detailsText += `📋 <b>إتاحة العقار:</b> ${propertyData.availability}\n`;
    }

    if (propertyData.imageStatus && propertyData.imageStatus !== 'غير محدد') {
      detailsText += `📸 <b>حالة الصور:</b> ${propertyData.imageStatus}\n`;
    }

    if (propertyData.price && propertyData.price > 0) {
      detailsText += `💰 <b>السعر:</b> ${propertyData.price.toLocaleString()} جنيه\n`;
    }

    if (propertyData.size && propertyData.size > 0) {
      detailsText += `📐 <b>المساحة:</b> ${propertyData.size} متر مربع\n`;
    }

    // إضافة الروابط
    detailsText += `\n🔗 <b>الروابط:</b>\n`;

    if (links.sheetLink) {
      detailsText += `📊 <a href="${links.sheetLink}">رابط Google Sheet</a>\n`;
    }

    if (links.notionLink) {
      detailsText += `📝 <a href="${links.notionLink}">رابط صفحة Notion</a>\n`;
    }

    if (links.zohoLink) {
      detailsText += `🗂️ <a href="${links.zohoLink}">رابط سجل Zoho</a>\n`;
    }

    // إضافة البيان الكامل إذا كان متوفراً
    if (propertyData.fullStatement && propertyData.fullStatement.trim()) {
      detailsText += `\n📄 <b>البيان الكامل:</b>\n${propertyData.fullStatement}`;
    }

    // بناء البطاقة
    const card = {
      cards: [{
        header: {
          title: '✅ عقار ناجح',
          subtitle: `كود الوحدة: ${unitCode}`,
          imageStyle: 'IMAGE'
        },
        sections: [{
          widgets: [{
            textParagraph: {
              text: detailsText
            }
          }]
        }]
      }]
    };

    Logger.log('تم بناء إشعار النجاح المفصل');
    return card;

  } catch (error) {
    Logger.log('خطأ في بناء إشعار النجاح: ' + error.toString());

    // رسالة بسيطة في حالة الخطأ
    return {
      text: `✅ تم تخزين العقار ${unitCode} بنجاح`
    };
  }
}

/**
 * بناء رسالة إشعار الفشل
 *
 * @param {string} unitCode - كود الوحدة
 * @param {string} failureReason - سبب الفشل
 * @param {Array} completedSteps - الخطوات المكتملة
 * @param {string} rawStatement - البيان الخام
 * @returns {Object} محتوى رسالة الفشل
 */
function buildFailureNotification(unitCode, failureReason, completedSteps = [], rawStatement = '') {
  try {
    Logger.log(`بناء إشعار الفشل - كود: ${unitCode}`);

    let detailsText = `❌ <b>عقار فاشل</b>\n\n`;

    detailsText += `🏠 <b>كود الوحدة:</b> ${unitCode}\n`;
    detailsText += `⏰ <b>وقت الفشل:</b> ${new Date().toLocaleString('ar-EG')}\n`;

    if (failureReason) {
      detailsText += `🚫 <b>سبب الفشل:</b> ${failureReason}\n`;
    }

    // عرض المراحل المكتملة والفاشلة
    detailsText += `\n📊 <b>حالة المراحل:</b>\n`;

    const allSteps = ['تخزين', 'تفكيك', 'نوشن', 'زوهو', 'اشعار'];

    for (const step of allSteps) {
      if (completedSteps.includes(step)) {
        detailsText += `✔️ ${step}\n`;
      } else {
        detailsText += `✖️ ${step}\n`;
      }
    }

    // إضافة البيان الخام
    if (rawStatement && rawStatement.trim()) {
      detailsText += `\n📄 <b>البيان الخام:</b>\n${rawStatement}`;
    }

    // بناء البطاقة
    const card = {
      cards: [{
        header: {
          title: '❌ عقار فاشل',
          subtitle: `كود الوحدة: ${unitCode}`,
          imageStyle: 'IMAGE'
        },
        sections: [{
          widgets: [{
            textParagraph: {
              text: detailsText
            }
          }]
        }, {
          widgets: [{
            buttons: [{
              textButton: {
                text: '🔄 إعادة المحاولة',
                onClick: {
                  action: {
                    actionMethodName: 'retryProcessing',
                    parameters: [{
                      key: 'unitCode',
                      value: unitCode
                    }]
                  }
                }
              }
            }]
          }]
        }]
      }]
    };

    Logger.log('تم بناء إشعار الفشل');
    return card;

  } catch (error) {
    Logger.log('خطأ في بناء إشعار الفشل: ' + error.toString());

    // رسالة بسيطة في حالة الخطأ
    return {
      text: `❌ فشل في معالجة العقار ${unitCode}: ${failureReason || 'خطأ غير محدد'}`
    };
  }
}

// ===================================================================
// نهاية ملف notifications.js - المرحلة الأولى
// ===================================================================
