/**
 * ===================================================================
 * AQAR BOT - QUICK TEST
 * ===================================================================
 * 
 * ملف اختبار سريع للنظام المحدث
 * يختبر نظام الإشعار الواحد لكل عقار
 * 
 * <AUTHOR> Bot Team
 * @version 1.0.0 - المرحلة الأولى (إشعار واحد محدث)
 * @created 2025-01-31
 * ===================================================================
 */

/**
 * اختبار سريع شامل للنظام المحدث
 */
function quickTestUpdatedSystem() {
  try {
    Logger.log('⚡ === اختبار سريع للنظام المحدث ===');
    
    // 1. اختبار الإعدادات الأساسية
    Logger.log('1️⃣ اختبار الإعدادات الأساسية...');
    const settingsOk = testBasicSettings();
    Logger.log(`الإعدادات: ${settingsOk ? '✅' : '❌'}`);
    
    // 2. اختبار بناء رسالة Telegram المحدثة
    Logger.log('2️⃣ اختبار بناء رسالة Telegram المحدثة...');
    const telegramOk = testTelegramMessageWithMultipleStates();
    Logger.log(`رسالة Telegram: ${telegramOk ? '✅' : '❌'}`);
    
    // 3. اختبار نظام الإشعار الواحد
    Logger.log('3️⃣ اختبار نظام الإشعار الواحد...');
    const singleNotificationOk = testSingleNotificationFlow();
    Logger.log(`الإشعار الواحد: ${singleNotificationOk ? '✅' : '❌'}`);
    
    // 4. اختبار دالة الحصول على حالات النجاح
    Logger.log('4️⃣ اختبار دالة حالات النجاح...');
    const successStatesOk = testGetAllSuccessStates();
    Logger.log(`حالات النجاح: ${successStatesOk ? '✅' : '❌'}`);
    
    // النتيجة النهائية
    const allTests = [settingsOk, telegramOk, singleNotificationOk, successStatesOk];
    const passedTests = allTests.filter(test => test).length;
    const totalTests = allTests.length;
    
    Logger.log('\n📊 === النتيجة النهائية ===');
    Logger.log(`الاختبارات الناجحة: ${passedTests}/${totalTests}`);
    Logger.log(`معدل النجاح: ${Math.round((passedTests/totalTests) * 100)}%`);
    
    if (passedTests === totalTests) {
      Logger.log('🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام');
      Logger.log('✨ كل عقار سيكون له إشعار واحد يتم تحديثه مع كل حالة نجاح');
    } else {
      Logger.log('⚠️ بعض الاختبارات فشلت - راجع التفاصيل أعلاه');
    }
    
    Logger.log('=== انتهاء الاختبار السريع ===');
    
  } catch (error) {
    Logger.log('❌ خطأ في الاختبار السريع: ' + error.toString());
  }
}

/**
 * اختبار الإعدادات الأساسية
 */
function testBasicSettings() {
  try {
    const properties = PropertiesService.getScriptProperties();
    
    const requiredSettings = [
      'SHEET_ID',
      'SHEET_NAME', 
      'TELEGRAM_CONTROLER_BOT_TOKEN',
      'TELEGRAM_ADMIN_CHAT_ID',
      'DISCORD_WEBHOOK_URL'
    ];
    
    let allSettingsOk = true;
    
    for (const setting of requiredSettings) {
      const value = properties.getProperty(setting);
      if (!value) {
        Logger.log(`❌ الإعداد مفقود: ${setting}`);
        allSettingsOk = false;
      } else {
        Logger.log(`✅ ${setting}: موجود`);
      }
    }
    
    return allSettingsOk;
    
  } catch (error) {
    Logger.log('خطأ في اختبار الإعدادات: ' + error.toString());
    return false;
  }
}

/**
 * اختبار تدفق الإشعار الواحد
 */
function testSingleNotificationFlow() {
  try {
    const testUnitCode = `QUICK-TEST-${Date.now()}`;
    
    Logger.log(`اختبار تدفق الإشعار الواحد للعقار: ${testUnitCode}`);
    
    // محاكاة المراحل المختلفة
    const stages = [
      { status: 'تخزين', details: 'تم استقبال البيان' },
      { status: 'تفكيك', details: 'تم تحليل البيان' },
      { status: 'نوشن', details: 'تم توثيق العقار' },
      { status: 'زوهو', details: 'تم تسجيل العقار' },
      { status: 'اشعار', details: 'تم إشعار العميل' }
    ];
    
    for (let i = 0; i < stages.length; i++) {
      const stage = stages[i];
      Logger.log(`المرحلة ${i + 1}: ${stage.status}`);
      
      // بناء رسالة Telegram للمرحلة
      const message = buildTelegramMessage(testUnitCode, stage.status, stage.details);
      
      if (!message) {
        Logger.log(`❌ فشل بناء رسالة للمرحلة: ${stage.status}`);
        return false;
      }
      
      // التحقق من وجود العناصر المطلوبة
      if (!message.includes(testUnitCode)) {
        Logger.log(`❌ كود الوحدة مفقود في المرحلة: ${stage.status}`);
        return false;
      }
      
      if (!message.includes('حالات النجاح')) {
        Logger.log(`❌ قسم حالات النجاح مفقود في المرحلة: ${stage.status}`);
        return false;
      }
      
      if (!message.includes('نسبة الإنجاز')) {
        Logger.log(`❌ نسبة الإنجاز مفقودة في المرحلة: ${stage.status}`);
        return false;
      }
      
      Logger.log(`✅ المرحلة ${i + 1} (${stage.status}) نجحت`);
    }
    
    Logger.log('✅ جميع مراحل الإشعار الواحد نجحت');
    return true;
    
  } catch (error) {
    Logger.log('خطأ في اختبار تدفق الإشعار الواحد: ' + error.toString());
    return false;
  }
}

/**
 * عرض عينة من رسالة Telegram المحدثة
 */
function showTelegramMessageSample() {
  try {
    Logger.log('📱 === عينة من رسالة Telegram المحدثة ===');
    
    const sampleUnitCode = 'SAMPLE-31.01.2025-001';
    const sampleStatus = 'نوشن';
    const sampleDetails = 'تم توثيق العقار في Notion بنجاح وإضافة جميع التفاصيل المطلوبة';
    
    const message = buildTelegramMessage(sampleUnitCode, sampleStatus, sampleDetails);
    
    Logger.log(message);
    Logger.log('=== انتهاء العينة ===');
    
    // إحصائيات الرسالة
    Logger.log(`📊 طول الرسالة: ${message.length} حرف`);
    Logger.log(`📊 عدد الأسطر: ${message.split('\n').length}`);
    
    // التحقق من العناصر الرئيسية
    const keyElements = [
      '🏠 كود الوحدة',
      '📊 حالات النجاح',
      '📈 نسبة الإنجاز',
      '✅', // علامة الإنجاز
      '⏳', // علامة الانتظار
      '[', // شريط التقدم
      '#' // هاشتاج
    ];
    
    Logger.log('🔍 العناصر الموجودة:');
    keyElements.forEach(element => {
      const found = message.includes(element);
      Logger.log(`  ${found ? '✅' : '❌'} ${element}`);
    });
    
  } catch (error) {
    Logger.log('خطأ في عرض عينة الرسالة: ' + error.toString());
  }
}

/**
 * اختبار مقارنة بين النظام القديم والجديد
 */
function compareOldVsNewSystem() {
  try {
    Logger.log('🔄 === مقارنة النظام القديم والجديد ===');
    
    const testUnitCode = 'COMPARE-31.01.2025-001';
    
    Logger.log('📱 النظام الجديد (إشعار واحد محدث):');
    Logger.log('✅ إشعار واحد لكل عقار');
    Logger.log('✅ يتم تحديث الرسالة مع كل حالة نجاح');
    Logger.log('✅ عرض جميع حالات النجاح في رسالة واحدة');
    Logger.log('✅ شريط تقدم يوضح نسبة الإنجاز');
    Logger.log('✅ تنظيم أفضل للمعلومات');
    
    Logger.log('\n📱 النظام القديم (إشعارات متعددة):');
    Logger.log('❌ إشعار منفصل لكل حالة');
    Logger.log('❌ تكرار في الإشعارات');
    Logger.log('❌ صعوبة في تتبع حالة العقار الكاملة');
    Logger.log('❌ ازدحام في قناة الإشعارات');
    
    // عرض عينة من الرسالة الجديدة
    const newMessage = buildTelegramMessage(testUnitCode, 'تفكيك', 'تم تحليل البيان بنجاح');
    
    Logger.log('\n📝 عينة من الرسالة الجديدة:');
    Logger.log('─'.repeat(50));
    Logger.log(newMessage.substring(0, 300) + '...');
    Logger.log('─'.repeat(50));
    
    Logger.log('\n🎯 الفوائد الرئيسية للنظام الجديد:');
    Logger.log('1. تقليل عدد الإشعارات بنسبة 80%');
    Logger.log('2. رؤية شاملة لحالة العقار في مكان واحد');
    Logger.log('3. سهولة متابعة تقدم معالجة العقار');
    Logger.log('4. تنظيم أفضل لقناة الإشعارات');
    Logger.log('5. معلومات أكثر تفصيلاً في رسالة واحدة');
    
  } catch (error) {
    Logger.log('خطأ في المقارنة: ' + error.toString());
  }
}

/**
 * اختبار إرسال إشعار تجريبي فعلي (استخدم بحذر!)
 */
function sendRealTestNotification() {
  try {
    Logger.log('⚠️ === إرسال إشعار تجريبي فعلي ===');
    Logger.log('هذا سيرسل إشعار فعلي إلى Telegram و Discord!');
    
    const testUnitCode = `REAL-TEST-${new Date().toLocaleDateString('en-GB').replace(/\//g, '.')}-${String(Date.now()).slice(-3)}`;
    
    // إرسال الإشعار الأولي
    Logger.log('1. إرسال إشعار أولي (تخزين)...');
    const result1 = sendOrUpdateNotification(testUnitCode, 'تخزين', 'اختبار فعلي للنظام الجديد - إشعار أولي');
    Logger.log(`نتيجة الإرسال الأولي: ${result1 ? '✅' : '❌'}`);
    
    if (result1) {
      // انتظار قصير ثم تحديث الإشعار
      Utilities.sleep(2000); // انتظار ثانيتين
      
      Logger.log('2. تحديث الإشعار (تفكيك)...');
      const result2 = sendOrUpdateNotification(testUnitCode, 'تفكيك', 'اختبار فعلي للنظام الجديد - تحديث الإشعار');
      Logger.log(`نتيجة التحديث: ${result2 ? '✅' : '❌'}`);
      
      if (result2) {
        // تحديث أخير
        Utilities.sleep(2000); // انتظار ثانيتين
        
        Logger.log('3. تحديث نهائي (نوشن)...');
        const result3 = sendOrUpdateNotification(testUnitCode, 'نوشن', 'اختبار فعلي للنظام الجديد - تحديث نهائي');
        Logger.log(`نتيجة التحديث النهائي: ${result3 ? '✅' : '❌'}`);
        
        if (result3) {
          Logger.log('🎉 تم اختبار النظام الجديد بنجاح!');
          Logger.log(`📱 تحقق من Telegram و Discord للعقار: ${testUnitCode}`);
          Logger.log('✨ يجب أن ترى إشعار واحد تم تحديثه 3 مرات');
        }
      }
    }
    
  } catch (error) {
    Logger.log('❌ خطأ في الاختبار الفعلي: ' + error.toString());
  }
}

// ===================================================================
// نهاية ملف quick_test.js - المرحلة الأولى
// ===================================================================
