/**
 * ===================================================================
 * AQAR BOT - SCRIPT PROPERTIES SETUP
 * ===================================================================
 * 
 * ملف إعداد خصائص السكريبت
 * يحتوي على دوال تكوين جميع المتغيرات والإعدادات المطلوبة
 * 
 * الوظائف الرئيسية:
 * - setupAllProperties: إعداد جميع الخصائص
 * - setupGoogleServices: إعداد خدمات Google
 * - setupAIServices: إعداد خدمات الذكاء الاصطناعي
 * - setupNotifications: إعداد الإشعارات
 * 
 * <AUTHOR> Bot Team
 * @version 1.0.0 - المرحلة الأولى
 * @created 2025-01-31
 * ===================================================================
 */

/**
 * إعداد جميع خصائص السكريبت من ملف .env
 * يجب تشغيل هذه الدالة مرة واحدة بعد إنشاء المشروع
 */
function setupAllProperties() {
  try {
    Logger.log('=== بدء إعداد جميع خصائص السكريبت ===');
    
    const properties = PropertiesService.getScriptProperties();
    
    // إعداد خدمات Google
    setupGoogleServices(properties);
    
    // إعداد خدمات الذكاء الاصطناعي
    setupAIServices(properties);
    
    // إعداد الإشعارات
    setupNotifications(properties);
    
    // إعداد أسماء الحقول العربية
    setupFieldNames(properties);
    
    // إعداد القيم الافتراضية
    setupDefaultValues(properties);
    
    Logger.log('=== تم إعداد جميع الخصائص بنجاح ===');
    
    // عرض ملخص الإعدادات
    displayPropertiesSummary();
    
  } catch (error) {
    Logger.log('خطأ في إعداد الخصائص: ' + error.toString());
  }
}

/**
 * إعداد خدمات Google
 */
function setupGoogleServices(properties) {
  try {
    Logger.log('إعداد خدمات Google...');
    
    const googleServices = {
      // Google Forms
      'GOOGLE_FORM_ID': '1FAIpQLSfcVgW2pUOz9_NNBcoo4pT_KQmNM5bnVPTL0CmN_VM-6cAKsw',
      'GOOGLE_FORM_URL': 'https://docs.google.com/forms/d/e/1FAIpQLSfcVgW2pUOz9_NNBcoo4pT_KQmNM5bnVPTL0CmN_VM-6cAKsw/viewform',
      
      // Google Sheets
      'SHEET_ID': '1Qwfv9m4mUJ6NB0aFrTqgTxUKdkJggq67zm63T4HqR1c',
      'SHEET_NAME': 'Aqar_bot',
      'NOTIFICATIONS_LOG_SHEET_ID': '1L7cD6H1kW6xR7B0g8WSl-7rs-AM6c5AEq_UJpEFzhKM',
      
      // Google Docs
      'PROMPT_DOC_ID': '14dYTyvqn1V38OtH7TJy1GDoww34NgRUDbVZCySWWpvk',
      
      // Google Drive
      'GOOGLE_DRIVE_FOLDER_ID': '13wQOTm5njDPrdT7tY4O9KtnXJOqt'
    };
    
    for (const [key, value] of Object.entries(googleServices)) {
      properties.setProperty(key, value);
      Logger.log(`✓ تم إعداد ${key}`);
    }
    
    Logger.log('تم إعداد خدمات Google بنجاح');
    
  } catch (error) {
    Logger.log('خطأ في إعداد خدمات Google: ' + error.toString());
  }
}

/**
 * إعداد خدمات الذكاء الاصطناعي
 */
function setupAIServices(properties) {
  try {
    Logger.log('إعداد خدمات الذكاء الاصطناعي...');
    
    const aiServices = {
      // OpenAI
      'OPENAI_API_KEY': '********************************************************************************************************************************************************************',
      'OPENAI_MODEL': 'gpt-4',
      
      // Mistral AI
      'MISTRAL_API_KEY': 'soMr4s2jGPzGrKO00BOjOh7Vrhb5IxMP',
      'MISTRAL_MODEL': 'mistral-large',
      
      // Groq
      'GROQ_API_KEY': '********************************************************',
      'GROQ_MODEL': 'llama3-70b-8192',
      
      // HuggingFace
      'HUGGINGFACE_API_KEY': '*************************************',
      'HUGGINGFACE_MODEL': 'meta-llama/Llama-2-70b-chat-hf',
      
      // Google Gemini (متعدد المفاتيح)
      'GEMINI_API_KEY_1': 'AIzaSyC6hgtBF5ILBI8WGOmjavjEO3b699cL8A8',
      'GEMINI_API_KEY_2': 'AIzaSyDIEBBrQLsdJO3tIELc0zFuJVk1efZOo9c',
      'GEMINI_API_KEY_3': 'AIzaSyDboufcqfd5iVhmO3eRogqA4e3FUJz_fw8',
      'GEMINI_MODEL': 'gemini-pro'
    };
    
    for (const [key, value] of Object.entries(aiServices)) {
      properties.setProperty(key, value);
      Logger.log(`✓ تم إعداد ${key}`);
    }
    
    Logger.log('تم إعداد خدمات الذكاء الاصطناعي بنجاح');
    
  } catch (error) {
    Logger.log('خطأ في إعداد خدمات الذكاء الاصطناعي: ' + error.toString());
  }
}

/**
 * إعداد خدمات الإشعارات
 */
function setupNotifications(properties) {
  try {
    Logger.log('إعداد خدمات الإشعارات...');
    
    const notificationServices = {
      // Telegram
      'TELEGRAM_API_ID': '23358202',
      'TELEGRAM_API_HASH': 'd66eecdb7446f53d7d591d579de535ca',
      'TELEGRAM_CONTROLER_BOT_USERNAME': '@SagdaAyman_bot',
      'TELEGRAM_CONTROLER_BOT_TOKEN': '**********************************************',
      'TELEGRAM_ADMIN_CHAT_ID': '-1002711636474',
      
      // Discord
      'DISCORD_BOT_NAME': 'elsweedy real estate bot',
      'DISCORD_BOT_TOKEN': 'MTQwMDI3NjEwNzAwOTE5NjE4NA.G2eKDe.YaGycjx-YKwZCpOQBUGOwAgPsDEKvHJgDt-GYE',
      'DISCORD_CHANNEL_URL': 'https://discord.com/channels/1400274670338445435/1400275224800530584',
      'DISCORD_CHANNEL_ID': '1400275224800530584',
      'DISCORD_WEBHOOK_URL': 'https://discord.com/api/webhooks/1400275298678734918/FYx149A3arfBGigEiQ-YAIeFCpPmgGuQIuqVvcr3NwTGvBZJVqkHhUSJoM1gQJ0NsvIb'
    };
    
    for (const [key, value] of Object.entries(notificationServices)) {
      properties.setProperty(key, value);
      Logger.log(`✓ تم إعداد ${key}`);
    }
    
    Logger.log('تم إعداد خدمات الإشعارات بنجاح');
    
  } catch (error) {
    Logger.log('خطأ في إعداد خدمات الإشعارات: ' + error.toString());
  }
}

/**
 * إعداد أسماء الحقول العربية
 */
function setupFieldNames(properties) {
  try {
    Logger.log('إعداد أسماء الحقول العربية...');
    
    const fieldNames = {
      'UNIT_CODE_COL': 'كود الوحدة',
      'STATUS_COL': 'Status',
      'SUCCESS_STATES_COL': 'حالات النجاح',
      'PROPERTY_TYPE_QUESTION': 'نوع البيان اللي بتسجله     ❓❔',
      'SINGLE_VALUE_FIELD': '🪀  بيان  عقار  غير مفصل',
      'DETAILED_TYPE': 'بيان  مفصل  💘',
      'MULTI_VALUE_FIELD': '🪀  بيانات  متعددة  غير مفصله'
    };
    
    for (const [key, value] of Object.entries(fieldNames)) {
      properties.setProperty(key, value);
      Logger.log(`✓ تم إعداد ${key}: ${value}`);
    }
    
    Logger.log('تم إعداد أسماء الحقول العربية بنجاح');
    
  } catch (error) {
    Logger.log('خطأ في إعداد أسماء الحقول: ' + error.toString());
  }
}

/**
 * إعداد القيم الافتراضية
 */
function setupDefaultValues(properties) {
  try {
    Logger.log('إعداد القيم الافتراضية...');
    
    const defaultValues = {
      'DEFAULT_TEXT_VALUE': 'غير محدد',
      'DEFAULT_NUMBER_VALUE': '0',
      'DEFAULT_PHONE_VALUE': '01000000000'
    };
    
    for (const [key, value] of Object.entries(defaultValues)) {
      properties.setProperty(key, value);
      Logger.log(`✓ تم إعداد ${key}: ${value}`);
    }
    
    Logger.log('تم إعداد القيم الافتراضية بنجاح');
    
  } catch (error) {
    Logger.log('خطأ في إعداد القيم الافتراضية: ' + error.toString());
  }
}

/**
 * عرض ملخص جميع الإعدادات
 */
function displayPropertiesSummary() {
  try {
    Logger.log('=== ملخص الإعدادات ===');
    
    const properties = PropertiesService.getScriptProperties();
    const allProperties = properties.getProperties();
    
    Logger.log(`إجمالي عدد الإعدادات: ${Object.keys(allProperties).length}`);
    
    // تجميع الإعدادات حسب النوع
    const categories = {
      'Google Services': ['GOOGLE_', 'SHEET_', 'PROMPT_', 'DRIVE_'],
      'AI Services': ['OPENAI_', 'MISTRAL_', 'GROQ_', 'HUGGINGFACE_', 'GEMINI_'],
      'Notifications': ['TELEGRAM_', 'DISCORD_'],
      'Field Names': ['_COL', '_FIELD', '_TYPE', '_QUESTION'],
      'Default Values': ['DEFAULT_']
    };
    
    for (const [category, prefixes] of Object.entries(categories)) {
      const categoryProps = Object.keys(allProperties).filter(key => 
        prefixes.some(prefix => key.includes(prefix))
      );
      
      Logger.log(`${category}: ${categoryProps.length} إعدادات`);
    }
    
    Logger.log('=== انتهاء ملخص الإعدادات ===');
    
  } catch (error) {
    Logger.log('خطأ في عرض ملخص الإعدادات: ' + error.toString());
  }
}

/**
 * اختبار الإعدادات
 */
function testProperties() {
  try {
    Logger.log('=== اختبار الإعدادات ===');
    
    const properties = PropertiesService.getScriptProperties();
    
    // اختبار الإعدادات الأساسية
    const essentialProperties = [
      'SHEET_ID',
      'SHEET_NAME',
      'NOTIFICATIONS_LOG_SHEET_ID',
      'DEFAULT_TEXT_VALUE',
      'UNIT_CODE_COL'
    ];
    
    let allGood = true;
    
    for (const prop of essentialProperties) {
      const value = properties.getProperty(prop);
      if (value) {
        Logger.log(`✓ ${prop}: موجود`);
      } else {
        Logger.log(`✗ ${prop}: مفقود`);
        allGood = false;
      }
    }
    
    if (allGood) {
      Logger.log('✅ جميع الإعدادات الأساسية موجودة');
    } else {
      Logger.log('⚠️ بعض الإعدادات الأساسية مفقودة');
    }
    
    Logger.log('=== انتهاء اختبار الإعدادات ===');
    
  } catch (error) {
    Logger.log('خطأ في اختبار الإعدادات: ' + error.toString());
  }
}

/**
 * حذف جميع الإعدادات (للاختبار فقط)
 */
function clearAllProperties() {
  try {
    Logger.log('⚠️ حذف جميع الإعدادات...');
    
    const properties = PropertiesService.getScriptProperties();
    properties.deleteAllProperties();
    
    Logger.log('تم حذف جميع الإعدادات');
    
  } catch (error) {
    Logger.log('خطأ في حذف الإعدادات: ' + error.toString());
  }
}

// ===================================================================
// نهاية ملف setup_properties.js - المرحلة الأولى
// ===================================================================
