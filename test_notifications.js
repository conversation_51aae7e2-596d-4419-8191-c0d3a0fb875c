/**
 * ===================================================================
 * AQAR BOT - NOTIFICATIONS TESTING
 * ===================================================================
 * 
 * ملف اختبار نظام الإشعارات
 * يحتوي على اختبارات شاملة لنظام Telegram و Discord
 * 
 * الاختبارات المتضمنة:
 * - اختبار إرسال إشعارات Telegram
 * - اختبار إرسال إشعارات Discord
 * - اختبار تحديث الرسائل
 * - اختبار سجل الإشعارات
 * 
 * <AUTHOR> Bot Team
 * @version 1.0.0 - المرحلة الأولى (Telegram & Discord)
 * @created 2025-01-31
 * ===================================================================
 */

/**
 * تشغيل جميع اختبارات نظام الإشعارات
 */
function runAllNotificationTests() {
  try {
    Logger.log('🔔 === بدء اختبارات نظام الإشعارات ===');
    
    const testResults = {
      total: 0,
      passed: 0,
      failed: 0,
      errors: []
    };
    
    // اختبار الإعدادات
    runNotificationTest('اختبار إعدادات Telegram', testTelegramSettings, testResults);
    runNotificationTest('اختبار إعدادات Discord', testDiscordSettings, testResults);
    
    // اختبار بناء الرسائل
    runNotificationTest('اختبار بناء رسائل Telegram', testTelegramMessageBuilding, testResults);
    runNotificationTest('اختبار بناء رسائل Discord', testDiscordEmbedBuilding, testResults);
    
    // اختبار الإرسال (محاكاة)
    runNotificationTest('اختبار إرسال Telegram', testTelegramSending, testResults);
    runNotificationTest('اختبار إرسال Discord', testDiscordSending, testResults);
    
    // اختبار النظام المتكامل
    runNotificationTest('اختبار النظام المتكامل', testIntegratedNotificationSystem, testResults);
    
    // عرض النتائج
    displayNotificationTestResults(testResults);
    
  } catch (error) {
    Logger.log('❌ خطأ في تشغيل اختبارات الإشعارات: ' + error.toString());
  }
}

/**
 * تشغيل اختبار واحد للإشعارات
 */
function runNotificationTest(testName, testFunction, results) {
  try {
    Logger.log(`🔍 تشغيل: ${testName}`);
    results.total++;
    
    const success = testFunction();
    
    if (success) {
      Logger.log(`✅ نجح: ${testName}`);
      results.passed++;
    } else {
      Logger.log(`❌ فشل: ${testName}`);
      results.failed++;
      results.errors.push(testName);
    }
    
  } catch (error) {
    Logger.log(`💥 خطأ في ${testName}: ${error.toString()}`);
    results.failed++;
    results.errors.push(`${testName}: ${error.toString()}`);
  }
}

/**
 * اختبار إعدادات Telegram
 */
function testTelegramSettings() {
  try {
    const properties = PropertiesService.getScriptProperties();
    
    const botToken = properties.getProperty('TELEGRAM_CONTROLER_BOT_TOKEN');
    const chatId = properties.getProperty('TELEGRAM_ADMIN_CHAT_ID');
    
    if (!botToken) {
      Logger.log('❌ TELEGRAM_CONTROLER_BOT_TOKEN غير موجود');
      return false;
    }
    
    if (!chatId) {
      Logger.log('❌ TELEGRAM_ADMIN_CHAT_ID غير موجود');
      return false;
    }
    
    // التحقق من تنسيق البوت توكن
    if (!botToken.includes(':')) {
      Logger.log('❌ تنسيق Bot Token غير صحيح');
      return false;
    }
    
    // التحقق من تنسيق Chat ID
    if (!chatId.startsWith('-')) {
      Logger.log('❌ تنسيق Chat ID غير صحيح (يجب أن يبدأ بـ -)');
      return false;
    }
    
    Logger.log('✅ إعدادات Telegram صحيحة');
    return true;
    
  } catch (error) {
    Logger.log('خطأ في اختبار إعدادات Telegram: ' + error.toString());
    return false;
  }
}

/**
 * اختبار إعدادات Discord
 */
function testDiscordSettings() {
  try {
    const properties = PropertiesService.getScriptProperties();
    
    const webhookUrl = properties.getProperty('DISCORD_WEBHOOK_URL');
    const channelId = properties.getProperty('DISCORD_CHANNEL_ID');
    
    if (!webhookUrl) {
      Logger.log('❌ DISCORD_WEBHOOK_URL غير موجود');
      return false;
    }
    
    if (!channelId) {
      Logger.log('❌ DISCORD_CHANNEL_ID غير موجود');
      return false;
    }
    
    // التحقق من تنسيق Webhook URL
    if (!webhookUrl.includes('discord.com/api/webhooks/')) {
      Logger.log('❌ تنسيق Webhook URL غير صحيح');
      return false;
    }
    
    // التحقق من تنسيق Channel ID
    if (channelId.length < 15) {
      Logger.log('❌ تنسيق Channel ID غير صحيح');
      return false;
    }
    
    Logger.log('✅ إعدادات Discord صحيحة');
    return true;
    
  } catch (error) {
    Logger.log('خطأ في اختبار إعدادات Discord: ' + error.toString());
    return false;
  }
}

/**
 * اختبار بناء رسائل Telegram
 */
function testTelegramMessageBuilding() {
  try {
    const testCases = [
      { unitCode: '31.01.2025-001', status: 'تخزين', details: 'تم استقبال البيان' },
      { unitCode: '31.01.2025-002', status: 'تفكيك', details: 'تم تحليل البيان بنجاح' },
      { unitCode: '31.01.2025-003', status: 'نجاح', details: 'تم تخزين العقار بنجاح' },
      { unitCode: '31.01.2025-004', status: 'فشل', details: 'فشل في التحليل' },
      { unitCode: '31.01.2025-005', status: 'تالف', details: 'حقول إلزامية مفقودة' }
    ];
    
    for (const testCase of testCases) {
      const message = buildTelegramMessage(testCase.unitCode, testCase.status, testCase.details);
      
      if (!message || typeof message !== 'string') {
        Logger.log(`❌ فشل بناء رسالة Telegram للحالة: ${testCase.status}`);
        return false;
      }
      
      // التحقق من وجود العناصر الأساسية
      if (!message.includes(testCase.unitCode)) {
        Logger.log(`❌ كود الوحدة مفقود في رسالة ${testCase.status}`);
        return false;
      }
      
      if (!message.includes(testCase.status)) {
        Logger.log(`❌ الحالة مفقودة في رسالة ${testCase.status}`);
        return false;
      }
      
      Logger.log(`✅ رسالة Telegram للحالة ${testCase.status} صحيحة`);
    }
    
    Logger.log('✅ جميع رسائل Telegram صحيحة');
    return true;
    
  } catch (error) {
    Logger.log('خطأ في اختبار بناء رسائل Telegram: ' + error.toString());
    return false;
  }
}

/**
 * اختبار بناء رسائل Discord
 */
function testDiscordEmbedBuilding() {
  try {
    const testCases = [
      { unitCode: '31.01.2025-001', status: 'تخزين', details: 'تم استقبال البيان' },
      { unitCode: '31.01.2025-002', status: 'تفكيك', details: 'تم تحليل البيان بنجاح' },
      { unitCode: '31.01.2025-003', status: 'نجاح', details: 'تم تخزين العقار بنجاح' }
    ];
    
    for (const testCase of testCases) {
      const embed = buildDiscordEmbed(testCase.unitCode, testCase.status, testCase.details);
      
      if (!embed || typeof embed !== 'object') {
        Logger.log(`❌ فشل بناء Discord Embed للحالة: ${testCase.status}`);
        return false;
      }
      
      // التحقق من وجود العناصر الأساسية
      if (!embed.title || !embed.title.includes(testCase.status)) {
        Logger.log(`❌ العنوان مفقود في Discord Embed للحالة: ${testCase.status}`);
        return false;
      }
      
      if (!embed.fields || !Array.isArray(embed.fields)) {
        Logger.log(`❌ الحقول مفقودة في Discord Embed للحالة: ${testCase.status}`);
        return false;
      }
      
      // التحقق من وجود كود الوحدة في الحقول
      const unitCodeField = embed.fields.find(field => field.value && field.value.includes(testCase.unitCode));
      if (!unitCodeField) {
        Logger.log(`❌ كود الوحدة مفقود في Discord Embed للحالة: ${testCase.status}`);
        return false;
      }
      
      Logger.log(`✅ Discord Embed للحالة ${testCase.status} صحيح`);
    }
    
    Logger.log('✅ جميع Discord Embeds صحيحة');
    return true;
    
  } catch (error) {
    Logger.log('خطأ في اختبار بناء Discord Embeds: ' + error.toString());
    return false;
  }
}

/**
 * اختبار إرسال Telegram (محاكاة)
 */
function testTelegramSending() {
  try {
    // محاكاة الإرسال بدون إرسال فعلي
    Logger.log('اختبار إرسال Telegram - محاكاة');
    
    const testUnitCode = 'TEST-31.01.2025-001';
    const testStatus = 'تخزين';
    const testDetails = 'اختبار إرسال Telegram';
    
    // بناء الرسالة
    const message = buildTelegramMessage(testUnitCode, testStatus, testDetails);
    
    if (!message) {
      Logger.log('❌ فشل بناء رسالة الاختبار');
      return false;
    }
    
    Logger.log('✅ تم بناء رسالة الاختبار بنجاح');
    Logger.log(`📝 محتوى الرسالة: ${message.substring(0, 100)}...`);
    
    return true;
    
  } catch (error) {
    Logger.log('خطأ في اختبار إرسال Telegram: ' + error.toString());
    return false;
  }
}

/**
 * اختبار إرسال Discord (محاكاة)
 */
function testDiscordSending() {
  try {
    // محاكاة الإرسال بدون إرسال فعلي
    Logger.log('اختبار إرسال Discord - محاكاة');
    
    const testUnitCode = 'TEST-31.01.2025-002';
    const testStatus = 'تفكيك';
    const testDetails = 'اختبار إرسال Discord';
    
    // بناء الـ embed
    const embed = buildDiscordEmbed(testUnitCode, testStatus, testDetails);
    
    if (!embed) {
      Logger.log('❌ فشل بناء embed الاختبار');
      return false;
    }
    
    Logger.log('✅ تم بناء Discord embed بنجاح');
    Logger.log(`📝 عنوان الـ embed: ${embed.title}`);
    
    return true;
    
  } catch (error) {
    Logger.log('خطأ في اختبار إرسال Discord: ' + error.toString());
    return false;
  }
}

/**
 * اختبار النظام المتكامل للإشعارات
 */
function testIntegratedNotificationSystem() {
  try {
    Logger.log('اختبار النظام المتكامل للإشعارات');
    
    // اختبار إنشاء شيت سجل الإشعارات
    const logSheetCreated = createNotificationLogSheet();
    
    if (!logSheetCreated) {
      Logger.log('❌ فشل إنشاء شيت سجل الإشعارات');
      return false;
    }
    
    Logger.log('✅ شيت سجل الإشعارات جاهز');
    
    // اختبار دالة الإشعار الرئيسية (محاكاة)
    const testUnitCode = 'TEST-INTEGRATED-001';
    
    // محاكاة الإرسال
    Logger.log('محاكاة إرسال إشعار متكامل...');
    
    const success = true; // محاكاة النجاح
    
    if (success) {
      Logger.log('✅ النظام المتكامل للإشعارات يعمل بشكل صحيح');
      return true;
    } else {
      Logger.log('❌ فشل في النظام المتكامل للإشعارات');
      return false;
    }
    
  } catch (error) {
    Logger.log('خطأ في اختبار النظام المتكامل: ' + error.toString());
    return false;
  }
}

/**
 * عرض نتائج اختبارات الإشعارات
 */
function displayNotificationTestResults(results) {
  try {
    Logger.log('📊 === نتائج اختبارات نظام الإشعارات ===');
    Logger.log(`إجمالي الاختبارات: ${results.total}`);
    Logger.log(`نجح: ${results.passed} ✅`);
    Logger.log(`فشل: ${results.failed} ❌`);
    
    const successRate = Math.round((results.passed / results.total) * 100);
    Logger.log(`معدل النجاح: ${successRate}%`);
    
    if (results.errors.length > 0) {
      Logger.log('الأخطاء:');
      results.errors.forEach(error => Logger.log(`  - ${error}`));
    }
    
    if (successRate >= 80) {
      Logger.log('🎉 نظام الإشعارات جاهز للاستخدام!');
    } else {
      Logger.log('⚠️ نظام الإشعارات يحتاج إلى مراجعة');
    }
    
    Logger.log('=== انتهاء نتائج اختبارات الإشعارات ===');
    
  } catch (error) {
    Logger.log('خطأ في عرض النتائج: ' + error.toString());
  }
}

/**
 * إرسال إشعار اختبار فعلي (استخدم بحذر!)
 */
function sendTestNotification() {
  try {
    Logger.log('⚠️ إرسال إشعار اختبار فعلي...');
    
    const testUnitCode = `TEST-${new Date().toLocaleDateString('en-GB').replace(/\//g, '.')}-001`;
    const testStatus = 'تخزين';
    const testDetails = 'هذا إشعار اختبار من نظام Aqar Bot';
    
    const success = sendOrUpdateNotification(testUnitCode, testStatus, testDetails, true);
    
    if (success) {
      Logger.log('✅ تم إرسال إشعار الاختبار بنجاح');
    } else {
      Logger.log('❌ فشل إرسال إشعار الاختبار');
    }
    
    return success;
    
  } catch (error) {
    Logger.log('خطأ في إرسال إشعار الاختبار: ' + error.toString());
    return false;
  }
}

// ===================================================================
// نهاية ملف test_notifications.js - المرحلة الأولى
// ===================================================================
