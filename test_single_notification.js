/**
 * ===================================================================
 * AQAR BOT - SINGLE NOTIFICATION TESTING
 * ===================================================================
 * 
 * ملف اختبار نظام الإشعار الواحد المحدث
 * يختبر أن كل عقار له إشعار واحد يتم تحديثه مع كل حالة نجاح
 * 
 * <AUTHOR> Bot Team
 * @version 1.0.0 - المرحلة الأولى (إشعار واحد لكل عقار)
 * @created 2025-01-31
 * ===================================================================
 */

/**
 * اختبار شامل لنظام الإشعار الواحد
 */
function testSingleNotificationSystem() {
  try {
    Logger.log('🧪 === اختبار نظام الإشعار الواحد ===');
    
    const testResults = {
      total: 0,
      passed: 0,
      failed: 0,
      errors: []
    };
    
    // اختبار دالة الحصول على حالات النجاح
    runSingleNotificationTest('اختبار الحصول على حالات النجاح', testGetAllSuccessStates, testResults);
    
    // اختبار بناء رسالة Telegram المحدثة
    runSingleNotificationTest('اختبار بناء رسالة Telegram المحدثة', testUpdatedTelegramMessage, testResults);
    
    // اختبار سيناريو الإشعار الواحد
    runSingleNotificationTest('اختبار سيناريو الإشعار الواحد', testSingleNotificationScenario, testResults);
    
    // عرض النتائج
    displaySingleNotificationTestResults(testResults);
    
  } catch (error) {
    Logger.log('❌ خطأ في اختبار نظام الإشعار الواحد: ' + error.toString());
  }
}

/**
 * تشغيل اختبار واحد لنظام الإشعار الواحد
 */
function runSingleNotificationTest(testName, testFunction, results) {
  try {
    Logger.log(`🔍 تشغيل: ${testName}`);
    results.total++;
    
    const success = testFunction();
    
    if (success) {
      Logger.log(`✅ نجح: ${testName}`);
      results.passed++;
    } else {
      Logger.log(`❌ فشل: ${testName}`);
      results.failed++;
      results.errors.push(testName);
    }
    
  } catch (error) {
    Logger.log(`💥 خطأ في ${testName}: ${error.toString()}`);
    results.failed++;
    results.errors.push(`${testName}: ${error.toString()}`);
  }
}

/**
 * اختبار دالة الحصول على حالات النجاح
 */
function testGetAllSuccessStates() {
  try {
    Logger.log('اختبار دالة getAllSuccessStatesForUnit');
    
    // اختبار مع كود وحدة وهمي
    const testUnitCode = 'TEST-31.01.2025-001';
    
    // محاولة الحصول على حالات النجاح
    const successStates = getAllSuccessStatesForUnit(testUnitCode);
    
    // التحقق من أن الدالة ترجع مصفوفة
    if (!Array.isArray(successStates)) {
      Logger.log('❌ الدالة لا ترجع مصفوفة');
      return false;
    }
    
    Logger.log(`✅ الدالة ترجع مصفوفة: ${successStates.length} عنصر`);
    Logger.log(`حالات النجاح: ${successStates.join(', ')}`);
    
    return true;
    
  } catch (error) {
    Logger.log('خطأ في اختبار getAllSuccessStates: ' + error.toString());
    return false;
  }
}

/**
 * اختبار بناء رسالة Telegram المحدثة
 */
function testUpdatedTelegramMessage() {
  try {
    Logger.log('اختبار بناء رسالة Telegram المحدثة');
    
    const testCases = [
      {
        unitCode: 'TEST-31.01.2025-001',
        status: 'تخزين',
        details: 'تم استقبال البيان',
        expectedContent: ['📥', 'تخزين ✅', 'تفكيك ⏳']
      },
      {
        unitCode: 'TEST-31.01.2025-002', 
        status: 'تفكيك',
        details: 'تم تحليل البيان',
        expectedContent: ['🔍', 'حالات النجاح', 'نسبة الإنجاز']
      },
      {
        unitCode: 'TEST-31.01.2025-003',
        status: 'فشل',
        details: 'فشل في المعالجة',
        expectedContent: ['❌', 'عقار فاشل']
      }
    ];
    
    for (const testCase of testCases) {
      const message = buildTelegramMessage(testCase.unitCode, testCase.status, testCase.details);
      
      if (!message || typeof message !== 'string') {
        Logger.log(`❌ فشل بناء رسالة للحالة: ${testCase.status}`);
        return false;
      }
      
      // التحقق من وجود المحتوى المتوقع
      for (const expectedItem of testCase.expectedContent) {
        if (!message.includes(expectedItem)) {
          Logger.log(`❌ المحتوى المتوقع مفقود في رسالة ${testCase.status}: ${expectedItem}`);
          return false;
        }
      }
      
      // التحقق من وجود كود الوحدة
      if (!message.includes(testCase.unitCode)) {
        Logger.log(`❌ كود الوحدة مفقود في رسالة ${testCase.status}`);
        return false;
      }
      
      Logger.log(`✅ رسالة ${testCase.status} صحيحة`);
      Logger.log(`📝 عينة من الرسالة: ${message.substring(0, 100)}...`);
    }
    
    Logger.log('✅ جميع رسائل Telegram المحدثة صحيحة');
    return true;
    
  } catch (error) {
    Logger.log('خطأ في اختبار رسائل Telegram المحدثة: ' + error.toString());
    return false;
  }
}

/**
 * اختبار سيناريو الإشعار الواحد الكامل
 */
function testSingleNotificationScenario() {
  try {
    Logger.log('اختبار سيناريو الإشعار الواحد الكامل');
    
    const testUnitCode = `TEST-SINGLE-${Date.now()}`;
    
    // محاكاة إرسال إشعار أولي
    Logger.log('1. محاكاة إرسال إشعار أولي (تخزين)');
    const initialResult = simulateNotification(testUnitCode, 'تخزين', 'تم استقبال البيان');
    
    if (!initialResult) {
      Logger.log('❌ فشل في محاكاة الإشعار الأولي');
      return false;
    }
    
    // محاكاة تحديث الإشعار (تفكيك)
    Logger.log('2. محاكاة تحديث الإشعار (تفكيك)');
    const updateResult = simulateNotification(testUnitCode, 'تفكيك', 'تم تحليل البيان');
    
    if (!updateResult) {
      Logger.log('❌ فشل في محاكاة تحديث الإشعار');
      return false;
    }
    
    // محاكاة تحديث آخر (نوشن)
    Logger.log('3. محاكاة تحديث آخر (نوشن)');
    const finalResult = simulateNotification(testUnitCode, 'نوشن', 'تم توثيق العقار');
    
    if (!finalResult) {
      Logger.log('❌ فشل في محاكاة التحديث النهائي');
      return false;
    }
    
    Logger.log('✅ سيناريو الإشعار الواحد نجح بالكامل');
    return true;
    
  } catch (error) {
    Logger.log('خطأ في اختبار سيناريو الإشعار الواحد: ' + error.toString());
    return false;
  }
}

/**
 * محاكاة إرسال إشعار (بدون إرسال فعلي)
 */
function simulateNotification(unitCode, status, details) {
  try {
    Logger.log(`محاكاة إشعار: ${unitCode} - ${status}`);
    
    // محاكاة البحث عن إشعار موجود
    const existingNotification = findNotificationInLog(unitCode);
    
    if (existingNotification) {
      Logger.log(`✅ تم العثور على إشعار موجود - سيتم التحديث`);
      Logger.log(`Telegram ID: ${existingNotification.telegramMessageId}`);
      Logger.log(`Discord ID: ${existingNotification.discordMessageId}`);
    } else {
      Logger.log(`✅ لا يوجد إشعار سابق - سيتم إنشاء إشعار جديد`);
    }
    
    // محاكاة بناء الرسالة
    const telegramMessage = buildTelegramMessage(unitCode, status, details);
    
    if (!telegramMessage) {
      Logger.log('❌ فشل في بناء رسالة Telegram');
      return false;
    }
    
    Logger.log(`✅ تم بناء رسالة Telegram بنجاح`);
    Logger.log(`📝 طول الرسالة: ${telegramMessage.length} حرف`);
    
    // محاكاة بناء Discord Embed
    const discordEmbed = buildDiscordEmbed(unitCode, status, details);
    
    if (!discordEmbed) {
      Logger.log('❌ فشل في بناء Discord Embed');
      return false;
    }
    
    Logger.log(`✅ تم بناء Discord Embed بنجاح`);
    Logger.log(`📝 عنوان الـ embed: ${discordEmbed.title}`);
    
    return true;
    
  } catch (error) {
    Logger.log('خطأ في محاكاة الإشعار: ' + error.toString());
    return false;
  }
}

/**
 * عرض نتائج اختبارات نظام الإشعار الواحد
 */
function displaySingleNotificationTestResults(results) {
  try {
    Logger.log('📊 === نتائج اختبارات نظام الإشعار الواحد ===');
    Logger.log(`إجمالي الاختبارات: ${results.total}`);
    Logger.log(`نجح: ${results.passed} ✅`);
    Logger.log(`فشل: ${results.failed} ❌`);
    
    const successRate = Math.round((results.passed / results.total) * 100);
    Logger.log(`معدل النجاح: ${successRate}%`);
    
    if (results.errors.length > 0) {
      Logger.log('الأخطاء:');
      results.errors.forEach(error => Logger.log(`  - ${error}`));
    }
    
    if (successRate >= 80) {
      Logger.log('🎉 نظام الإشعار الواحد جاهز للاستخدام!');
      Logger.log('✨ كل عقار سيكون له إشعار واحد يتم تحديثه مع كل حالة نجاح');
    } else {
      Logger.log('⚠️ نظام الإشعار الواحد يحتاج إلى مراجعة');
    }
    
    Logger.log('=== انتهاء نتائج اختبارات نظام الإشعار الواحد ===');
    
  } catch (error) {
    Logger.log('خطأ في عرض النتائج: ' + error.toString());
  }
}

/**
 * اختبار سريع لرسالة Telegram مع حالات نجاح متعددة
 */
function testTelegramMessageWithMultipleStates() {
  try {
    Logger.log('🧪 اختبار رسالة Telegram مع حالات نجاح متعددة');
    
    const testUnitCode = 'DEMO-31.01.2025-001';
    const testStatus = 'نوشن';
    const testDetails = 'تم توثيق العقار في Notion بنجاح';
    
    // بناء الرسالة
    const message = buildTelegramMessage(testUnitCode, testStatus, testDetails);
    
    Logger.log('📱 === عينة من رسالة Telegram ===');
    Logger.log(message);
    Logger.log('=== انتهاء العينة ===');
    
    // التحقق من وجود العناصر المطلوبة
    const requiredElements = [
      'حالات النجاح',
      'نسبة الإنجاز',
      '📈',
      testUnitCode,
      testStatus
    ];
    
    let allElementsFound = true;
    
    for (const element of requiredElements) {
      if (!message.includes(element)) {
        Logger.log(`❌ العنصر المطلوب مفقود: ${element}`);
        allElementsFound = false;
      }
    }
    
    if (allElementsFound) {
      Logger.log('✅ جميع العناصر المطلوبة موجودة في الرسالة');
      return true;
    } else {
      Logger.log('❌ بعض العناصر المطلوبة مفقودة');
      return false;
    }
    
  } catch (error) {
    Logger.log('خطأ في اختبار رسالة Telegram: ' + error.toString());
    return false;
  }
}

// ===================================================================
// نهاية ملف test_single_notification.js - المرحلة الأولى
// ===================================================================
