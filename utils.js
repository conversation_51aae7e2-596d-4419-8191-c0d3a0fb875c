/**
 * ===================================================================
 * AQAR BOT - UTILITY FUNCTIONS
 * ===================================================================
 * 
 * ملف الدوال المساعدة العامة
 * يحتوي على جميع الدوال المساعدة التي تستخدم عبر المشروع
 * 
 * الوظائف الرئيسية:
 * - generateUnitCode: توليد كود الوحدة الفريد
 * - splitMultiValueField: تقسيم البيانات المتعددة
 * - getHeadersMap: الحصول على خريطة العناوين
 * - setDefaultValues: تطبيق القيم الافتراضية
 * 
 * <AUTHOR> Bot Team
 * @version 1.0.0 - المرحلة الأولى
 * @created 2025-01-31
 * ===================================================================
 */

/**
 * توليد كود وحدة فريد بالتنسيق DD.MM.YYYY-NNN
 * 
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet - ورقة العمل
 * @param {number} unitCodeColIndex - فهرس عمود كود الوحدة
 * @returns {string} كود الوحدة الجديد
 */
function generateUnitCode(sheet, unitCodeColIndex) {
  try {
    Logger.log('بدء توليد كود الوحدة');
    
    // الحصول على تاريخ اليوم بالتنسيق DD.MM.YYYY
    const today = new Date();
    const day = String(today.getDate()).padStart(2, '0');
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const year = today.getFullYear();
    const datePrefix = `${day}.${month}.${year}`;
    
    // البحث عن آخر كود لنفس اليوم
    const lastRow = sheet.getLastRow();
    let maxSerial = 0;
    
    if (lastRow > 1) {
      const unitCodes = sheet.getRange(2, unitCodeColIndex, lastRow - 1, 1).getValues();
      
      for (let i = 0; i < unitCodes.length; i++) {
        const code = unitCodes[i][0];
        if (code && typeof code === 'string' && code.startsWith(datePrefix)) {
          const parts = code.split('-');
          if (parts.length === 2) {
            const serial = parseInt(parts[1]);
            if (!isNaN(serial) && serial > maxSerial) {
              maxSerial = serial;
            }
          }
        }
      }
    }
    
    // توليد الرقم التسلسلي الجديد
    const newSerial = String(maxSerial + 1).padStart(3, '0');
    const newUnitCode = `${datePrefix}-${newSerial}`;
    
    Logger.log(`تم توليد كود الوحدة: ${newUnitCode}`);
    return newUnitCode;
    
  } catch (error) {
    Logger.log('خطأ في توليد كود الوحدة: ' + error.toString());
    // إرجاع كود افتراضي في حالة الخطأ
    const today = new Date();
    const day = String(today.getDate()).padStart(2, '0');
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const year = today.getFullYear();
    return `${day}.${month}.${year}-001`;
  }
}

/**
 * تقسيم النصوص المتعددة إلى بيانات منفصلة
 * 
 * @param {string} multiValueField - النص المتعدد القيم
 * @returns {Array<string>} مصفوفة البيانات المنفصلة
 */
function splitMultiValueField(multiValueField) {
  try {
    Logger.log('بدء تقسيم البيانات المتعددة');
    
    if (!multiValueField || typeof multiValueField !== 'string') {
      return [];
    }
    
    // تنظيف النص وتقسيمه
    const cleanText = multiValueField.trim();
    if (!cleanText) {
      return [];
    }
    
    // تقسيم النص بناءً على الأسطر الفارغة أو الرموز
    const lines = cleanText.split('\n');
    const statements = [];
    let currentStatement = '';
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // إذا كان السطر فارغ أو يحتوي على رموز فقط
      if (!line || /^[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\u0660-\u0669a-zA-Z0-9]+$/.test(line)) {
        if (currentStatement.trim()) {
          statements.push(currentStatement.trim());
          currentStatement = '';
        }
      } else {
        // إضافة السطر للبيان الحالي
        if (currentStatement) {
          currentStatement += '\n' + line;
        } else {
          currentStatement = line;
        }
      }
    }
    
    // إضافة البيان الأخير إذا كان موجوداً
    if (currentStatement.trim()) {
      statements.push(currentStatement.trim());
    }
    
    Logger.log(`تم تقسيم البيانات إلى ${statements.length} بيان`);
    return statements;
    
  } catch (error) {
    Logger.log('خطأ في تقسيم البيانات المتعددة: ' + error.toString());
    return [multiValueField]; // إرجاع النص الأصلي في حالة الخطأ
  }
}

/**
 * الحصول على خريطة العناوين من الصف الأول
 * 
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet - ورقة العمل
 * @returns {Object} خريطة العناوين {اسم_العمود: فهرس_العمود}
 */
function getHeadersMap(sheet) {
  try {
    Logger.log('بدء قراءة خريطة العناوين');
    
    const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
    const headersMap = {};
    
    for (let i = 0; i < headers.length; i++) {
      if (headers[i]) {
        headersMap[headers[i]] = i + 1; // فهرس العمود (1-based)
      }
    }
    
    Logger.log(`تم قراءة ${Object.keys(headersMap).length} عنوان`);
    return headersMap;
    
  } catch (error) {
    Logger.log('خطأ في قراءة خريطة العناوين: ' + error.toString());
    return {};
  }
}

/**
 * تطبيق القيم الافتراضية على صف جديد
 * 
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet - ورقة العمل
 * @param {number} rowNum - رقم الصف
 * @param {Object} headers - خريطة العناوين
 */
function setDefaultValues(sheet, rowNum, headers) {
  try {
    Logger.log(`بدء تطبيق القيم الافتراضية للصف ${rowNum}`);
    
    // القيم الافتراضية من الإعدادات
    const properties = PropertiesService.getScriptProperties();
    const defaultText = properties.getProperty('DEFAULT_TEXT_VALUE') || 'غير محدد';
    const defaultNumber = parseInt(properties.getProperty('DEFAULT_NUMBER_VALUE')) || 0;
    const defaultPhone = properties.getProperty('DEFAULT_PHONE_VALUE') || '01000000000';
    
    // تطبيق القيم الافتراضية للحقول المطلوبة
    const fieldsToUpdate = [
      { name: 'المنطقة', value: defaultText },
      { name: 'حالة الصور', value: defaultText },
      { name: 'Status', value: defaultText },
      { name: 'إتاحة العقار', value: defaultText },
      { name: 'حالة الوحدة', value: defaultText },
      { name: 'اسم الموظف', value: defaultText },
      { name: 'نوع الوحدة', value: defaultText },
      { name: 'الدور', value: defaultText },
      { name: 'المساحة', value: defaultNumber },
      { name: 'السعر', value: defaultNumber },
      { name: 'تفاصيل كاملة', value: defaultText },
      { name: 'العنوان', value: defaultText },
      { name: 'اسم المالك', value: defaultText },
      { name: 'رقم المالك', value: defaultPhone },
      { name: 'حالات النجاح', value: 'تخزين' }
    ];
    
    // تطبيق القيم على الخلايا الفارغة فقط
    for (const field of fieldsToUpdate) {
      if (headers[field.name]) {
        const currentValue = sheet.getRange(rowNum, headers[field.name]).getValue();
        if (!currentValue || currentValue === '') {
          sheet.getRange(rowNum, headers[field.name]).setValue(field.value);
        }
      }
    }
    
    // تطبيق التواريخ
    const now = new Date();
    if (headers['تاريخ آخر معالجة']) {
      sheet.getRange(rowNum, headers['تاريخ آخر معالجة']).setValue(now);
    }
    if (headers['تاريخ الإنشاء']) {
      const creationDate = sheet.getRange(rowNum, headers['تاريخ الإنشاء']).getValue();
      if (!creationDate) {
        sheet.getRange(rowNum, headers['تاريخ الإنشاء']).setValue(now);
      }
    }
    
    Logger.log('تم تطبيق القيم الافتراضية بنجاح');
    
  } catch (error) {
    Logger.log('خطأ في تطبيق القيم الافتراضية: ' + error.toString());
  }
}

/**
 * الحصول على معرف الشيت من الإعدادات
 * 
 * @returns {string} معرف الشيت الرئيسي
 */
function getMainSheetId() {
  const properties = PropertiesService.getScriptProperties();
  return properties.getProperty('SHEET_ID');
}

/**
 * الحصول على اسم ورقة العمل من الإعدادات
 *
 * @returns {string} اسم ورقة العمل
 */
function getMainSheetName() {
  const properties = PropertiesService.getScriptProperties();
  return properties.getProperty('SHEET_NAME') || 'Aqar_bot';
}

/**
 * تنظيف وتنسيق النصوص العربية
 *
 * @param {string} text - النص المراد تنظيفه
 * @returns {string} النص المنظف
 */
function cleanArabicText(text) {
  try {
    if (!text || typeof text !== 'string') {
      return '';
    }

    // إزالة المسافات الزائدة والأسطر الفارغة
    let cleanText = text.trim();
    cleanText = cleanText.replace(/\s+/g, ' '); // استبدال المسافات المتعددة بمسافة واحدة
    cleanText = cleanText.replace(/\n\s*\n/g, '\n'); // إزالة الأسطر الفارغة المتعددة

    return cleanText;

  } catch (error) {
    Logger.log('خطأ في تنظيف النص: ' + error.toString());
    return text || '';
  }
}

/**
 * استخراج رقم الهاتف من النص
 *
 * @param {string} text - النص المحتوي على رقم الهاتف
 * @returns {string} رقم الهاتف أو القيمة الافتراضية
 */
function extractPhoneNumber(text) {
  try {
    if (!text || typeof text !== 'string') {
      return '01000000000';
    }

    // البحث عن أرقام الهاتف المصرية
    const phoneRegex = /(?:01|٠١)[0-9٠-٩]{9}/g;
    const matches = text.match(phoneRegex);

    if (matches && matches.length > 0) {
      // تحويل الأرقام العربية إلى إنجليزية
      let phone = matches[0];
      phone = phone.replace(/[٠-٩]/g, (d) => '٠١٢٣٤٥٦٧٨٩'.indexOf(d));
      phone = phone.replace(/٠/g, '0').replace(/١/g, '1');

      // التأكد من أن الرقم 11 رقم
      if (phone.length === 11 && phone.startsWith('01')) {
        return phone;
      }
    }

    return '01000000000';

  } catch (error) {
    Logger.log('خطأ في استخراج رقم الهاتف: ' + error.toString());
    return '01000000000';
  }
}

/**
 * تحويل النص إلى رقم صحيح
 *
 * @param {string|number} value - القيمة المراد تحويلها
 * @returns {number} الرقم أو 0
 */
function parseToNumber(value) {
  try {
    if (typeof value === 'number') {
      return isNaN(value) ? 0 : value;
    }

    if (typeof value === 'string') {
      // إزالة المسافات والفواصل
      let cleanValue = value.trim().replace(/,/g, '');

      // تحويل الأرقام العربية إلى إنجليزية
      cleanValue = cleanValue.replace(/[٠-٩]/g, (d) => '٠١٢٣٤٥٦٧٨٩'.indexOf(d));

      const number = parseFloat(cleanValue);
      return isNaN(number) ? 0 : number;
    }

    return 0;

  } catch (error) {
    Logger.log('خطأ في تحويل النص إلى رقم: ' + error.toString());
    return 0;
  }
}

/**
 * التحقق من صحة البيانات الأساسية
 *
 * @param {Object} data - البيانات المراد التحقق منها
 * @returns {boolean} صحيح إذا كانت البيانات سليمة
 */
function validateBasicData(data) {
  try {
    if (!data || typeof data !== 'object') {
      return false;
    }

    // التحقق من وجود الحقول الأساسية
    const requiredFields = ['unitCode'];

    for (const field of requiredFields) {
      if (!data[field] || data[field] === '') {
        Logger.log(`الحقل المطلوب مفقود: ${field}`);
        return false;
      }
    }

    return true;

  } catch (error) {
    Logger.log('خطأ في التحقق من البيانات: ' + error.toString());
    return false;
  }
}

/**
 * تسجيل الأخطاء مع التفاصيل
 *
 * @param {string} functionName - اسم الدالة
 * @param {Error} error - كائن الخطأ
 * @param {Object} context - السياق الإضافي
 */
function logError(functionName, error, context = {}) {
  try {
    const errorMessage = `خطأ في ${functionName}: ${error.toString()}`;
    Logger.log(errorMessage);

    if (Object.keys(context).length > 0) {
      Logger.log(`السياق: ${JSON.stringify(context)}`);
    }

    // TODO: يمكن إضافة إرسال تقرير الخطأ لاحقاً

  } catch (logError) {
    Logger.log('خطأ في تسجيل الخطأ: ' + logError.toString());
  }
}

// ===================================================================
// نهاية ملف utils.js - المرحلة الأولى
// ===================================================================
