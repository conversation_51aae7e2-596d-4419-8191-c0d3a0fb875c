/**
 * ===================================================================
 * AQAR BOT - VALIDATION FUNCTIONS
 * ===================================================================
 * 
 * ملف دوال التحقق والفحص
 * يحتوي على جميع دوال التحقق من صحة البيانات وتصنيف العقارات
 * 
 * الوظائف الرئيسية:
 * - checkAndMarkDamaged: فحص وتصنيف العقارات التالفة
 * - updatePropertyStatus: تحديث حالة العقار
 * - validateRequiredFields: التحقق من الحقول الإلزامية
 * - classifyProperty: تصنيف العقار حسب النوع
 * 
 * <AUTHOR> Bot Team
 * @version 1.0.0 - المرحلة الأولى
 * @created 2025-01-31
 * ===================================================================
 */

/**
 * فحص وتصنيف العقار كتالف إذا كانت الحقول الإلزامية فارغة
 * 
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet - ورقة العمل
 * @param {number} rowNum - رقم الصف
 * @param {Object} headers - خريطة العناوين
 * @param {string} unitCode - كود الوحدة
 * @param {number} statusColIndex - فهرس عمود الحالة
 * @returns {boolean} true إذا كان العقار تالف
 */
function checkAndMarkDamaged(sheet, rowNum, headers, unitCode, statusColIndex) {
  try {
    Logger.log(`فحص العقار للتلف - كود الوحدة: ${unitCode}`);
    
    // الحقول الإلزامية منطقياً
    const requiredFields = ['المنطقة', 'نوع الوحدة', 'حالة الوحدة'];
    const defaultValue = 'غير محدد';
    let isDamaged = true;
    
    // فحص كل حقل إلزامي
    for (const fieldName of requiredFields) {
      if (headers[fieldName]) {
        const value = sheet.getRange(rowNum, headers[fieldName]).getValue();
        
        // إذا كان الحقل يحتوي على قيمة صحيحة (ليس فارغ وليس "غير محدد")
        if (value && value !== '' && value !== defaultValue) {
          isDamaged = false;
          break;
        }
      }
    }
    
    // إذا كان العقار تالف، تحديث الحالة
    if (isDamaged) {
      Logger.log(`العقار ${unitCode} مصنف كتالف - جميع الحقول الإلزامية فارغة`);
      
      // إضافة "عقار تالف" إلى حقل Status
      updatePropertyStatus(sheet, rowNum, statusColIndex, 'عقار تالف');
      
      // TODO: إرسال إشعار بالعقار التالف
      // sendOrUpdateNotification(unitCode, 'تالف', 'العقار يحتوي على حقول إلزامية فارغة');
      
      return true;
    }
    
    Logger.log(`العقار ${unitCode} سليم - يحتوي على حقول صحيحة`);
    return false;
    
  } catch (error) {
    Logger.log('خطأ في فحص العقار للتلف: ' + error.toString());
    return false;
  }
}

/**
 * تحديث حالة العقار في عمود Status (يدعم Multi-select)
 * 
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet - ورقة العمل
 * @param {number} rowNum - رقم الصف
 * @param {number} colIndex - فهرس العمود
 * @param {string} newStatus - الحالة الجديدة
 */
function updatePropertyStatus(sheet, rowNum, colIndex, newStatus) {
  try {
    Logger.log(`تحديث حالة العقار في الصف ${rowNum} إلى: ${newStatus}`);
    
    if (!colIndex || colIndex < 1) {
      Logger.log('فهرس العمود غير صحيح');
      return;
    }
    
    // قراءة القيمة الحالية
    const currentValue = sheet.getRange(rowNum, colIndex).getValue();
    let statusArray = [];
    
    // تحويل القيمة الحالية إلى مصفوفة
    if (currentValue && typeof currentValue === 'string') {
      statusArray = currentValue.split(',').map(s => s.trim()).filter(s => s);
    }
    
    // إضافة الحالة الجديدة إذا لم تكن موجودة
    if (!statusArray.includes(newStatus)) {
      statusArray.push(newStatus);
    }
    
    // تحديث القيمة في الشيت
    const updatedValue = statusArray.join(', ');
    sheet.getRange(rowNum, colIndex).setValue(updatedValue);
    
    Logger.log(`تم تحديث الحالة إلى: ${updatedValue}`);
    
  } catch (error) {
    Logger.log('خطأ في تحديث حالة العقار: ' + error.toString());
  }
}

/**
 * التحقق من صحة الحقول الإلزامية
 * 
 * @param {Object} propertyData - بيانات العقار
 * @returns {Object} نتيجة التحقق {isValid: boolean, missingFields: Array}
 */
function validateRequiredFields(propertyData) {
  try {
    Logger.log('بدء التحقق من الحقول الإلزامية');
    
    const requiredFields = [
      { name: 'المنطقة', key: 'area' },
      { name: 'نوع الوحدة', key: 'unitType' },
      { name: 'حالة الوحدة', key: 'unitStatus' }
    ];
    
    const missingFields = [];
    const defaultValue = 'غير محدد';
    
    for (const field of requiredFields) {
      const value = propertyData[field.key];
      
      if (!value || value === '' || value === defaultValue) {
        missingFields.push(field.name);
      }
    }
    
    const isValid = missingFields.length === 0;
    
    Logger.log(`نتيجة التحقق: ${isValid ? 'صحيح' : 'غير صحيح'}`);
    if (!isValid) {
      Logger.log(`الحقول المفقودة: ${missingFields.join(', ')}`);
    }
    
    return {
      isValid: isValid,
      missingFields: missingFields
    };
    
  } catch (error) {
    Logger.log('خطأ في التحقق من الحقول الإلزامية: ' + error.toString());
    return {
      isValid: false,
      missingFields: ['خطأ في التحقق']
    };
  }
}

/**
 * تصنيف العقار حسب النوع (متعدد، مكرر، تالف، عادي)
 * 
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet - ورقة العمل
 * @param {Object} propertyData - بيانات العقار
 * @param {number} currentRow - رقم الصف الحالي
 * @returns {string} نوع التصنيف
 */
function classifyProperty(sheet, propertyData, currentRow) {
  try {
    Logger.log('بدء تصنيف العقار');
    
    // التحقق من الحقول الإلزامية أولاً
    const validation = validateRequiredFields(propertyData);
    if (!validation.isValid) {
      Logger.log('العقار مصنف كتالف - حقول إلزامية مفقودة');
      return 'عقار تالف';
    }
    
    // فحص التكرار والتعدد
    const ownerPhone = propertyData.ownerPhone;
    
    if (ownerPhone && ownerPhone !== '01000000000') {
      // البحث عن عقارات أخرى لنفس المالك
      const duplicateCheck = checkForDuplicates(sheet, propertyData, currentRow);
      
      if (duplicateCheck.isExactDuplicate) {
        Logger.log('العقار مصنف كمكرر - تطابق تام');
        return 'عقار مكرر';
      } else if (duplicateCheck.hasMultipleProperties) {
        Logger.log('العقار مصنف كمتعدد - مالك متعدد العقارات');
        return 'عقار متعدد';
      }
    }
    
    Logger.log('العقار مصنف كعادي');
    return 'عقار عادي';
    
  } catch (error) {
    Logger.log('خطأ في تصنيف العقار: ' + error.toString());
    return 'عقار تالف';
  }
}

/**
 * فحص التكرار والتعدد في العقارات
 * 
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet - ورقة العمل
 * @param {Object} propertyData - بيانات العقار
 * @param {number} currentRow - رقم الصف الحالي
 * @returns {Object} نتيجة الفحص
 */
function checkForDuplicates(sheet, propertyData, currentRow) {
  try {
    Logger.log('فحص التكرار والتعدد');
    
    const headers = getHeadersMap(sheet);
    const lastRow = sheet.getLastRow();
    
    let hasMultipleProperties = false;
    let isExactDuplicate = false;
    
    if (lastRow > 1) {
      // قراءة جميع البيانات
      const allData = sheet.getRange(2, 1, lastRow - 1, sheet.getLastColumn()).getValues();
      
      for (let i = 0; i < allData.length; i++) {
        const rowIndex = i + 2; // الصف الفعلي في الشيت
        
        // تجاهل الصف الحالي
        if (rowIndex === currentRow) continue;
        
        const rowData = allData[i];
        const existingOwnerPhone = rowData[headers['رقم المالك'] - 1];
        
        // فحص نفس رقم المالك
        if (existingOwnerPhone === propertyData.ownerPhone) {
          hasMultipleProperties = true;
          
          // فحص التطابق التام
          const existingArea = rowData[headers['المنطقة'] - 1];
          const existingUnitType = rowData[headers['نوع الوحدة'] - 1];
          const existingUnitStatus = rowData[headers['حالة الوحدة'] - 1];
          const existingFloor = rowData[headers['الدور'] - 1];
          const existingSize = rowData[headers['المساحة'] - 1];
          
          if (existingArea === propertyData.area &&
              existingUnitType === propertyData.unitType &&
              existingUnitStatus === propertyData.unitStatus &&
              existingFloor === propertyData.floor &&
              existingSize === propertyData.size) {
            isExactDuplicate = true;
            break;
          }
        }
      }
    }
    
    return {
      hasMultipleProperties: hasMultipleProperties,
      isExactDuplicate: isExactDuplicate
    };
    
  } catch (error) {
    Logger.log('خطأ في فحص التكرار: ' + error.toString());
    return {
      hasMultipleProperties: false,
      isExactDuplicate: false
    };
  }
}

/**
 * تحديث حقل حالات النجاح
 *
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet - ورقة العمل
 * @param {number} rowNum - رقم الصف
 * @param {Object} headers - خريطة العناوين
 * @param {string} newState - الحالة الجديدة
 */
function updateSuccessStates(sheet, rowNum, headers, newState) {
  try {
    Logger.log(`تحديث حالات النجاح للصف ${rowNum} - إضافة: ${newState}`);

    const successStatesCol = headers['حالات النجاح'];
    if (!successStatesCol) {
      Logger.log('عمود حالات النجاح غير موجود');
      return;
    }

    // قراءة القيمة الحالية
    const currentValue = sheet.getRange(rowNum, successStatesCol).getValue();
    let statesArray = [];

    // تحويل القيمة الحالية إلى مصفوفة
    if (currentValue && typeof currentValue === 'string') {
      statesArray = currentValue.split(',').map(s => s.trim()).filter(s => s);
    }

    // إضافة الحالة الجديدة إذا لم تكن موجودة
    if (!statesArray.includes(newState)) {
      statesArray.push(newState);
    }

    // تحديث القيمة في الشيت
    const updatedValue = statesArray.join(', ');
    sheet.getRange(rowNum, successStatesCol).setValue(updatedValue);

    Logger.log(`تم تحديث حالات النجاح إلى: ${updatedValue}`);

  } catch (error) {
    Logger.log('خطأ في تحديث حالات النجاح: ' + error.toString());
  }
}

/**
 * إزالة حالة من حقل حالات النجاح
 *
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet - ورقة العمل
 * @param {number} rowNum - رقم الصف
 * @param {Object} headers - خريطة العناوين
 * @param {string} stateToRemove - الحالة المراد إزالتها
 */
function removeSuccessState(sheet, rowNum, headers, stateToRemove) {
  try {
    Logger.log(`إزالة حالة النجاح للصف ${rowNum} - إزالة: ${stateToRemove}`);

    const successStatesCol = headers['حالات النجاح'];
    if (!successStatesCol) {
      Logger.log('عمود حالات النجاح غير موجود');
      return;
    }

    // قراءة القيمة الحالية
    const currentValue = sheet.getRange(rowNum, successStatesCol).getValue();
    let statesArray = [];

    // تحويل القيمة الحالية إلى مصفوفة
    if (currentValue && typeof currentValue === 'string') {
      statesArray = currentValue.split(',').map(s => s.trim()).filter(s => s);
    }

    // إزالة الحالة المحددة
    statesArray = statesArray.filter(state => state !== stateToRemove);

    // تحديث القيمة في الشيت
    const updatedValue = statesArray.join(', ');
    sheet.getRange(rowNum, successStatesCol).setValue(updatedValue);

    Logger.log(`تم تحديث حالات النجاح إلى: ${updatedValue}`);

  } catch (error) {
    Logger.log('خطأ في إزالة حالة النجاح: ' + error.toString());
  }
}

/**
 * التحقق من وجود حالة معينة في حالات النجاح
 *
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet - ورقة العمل
 * @param {number} rowNum - رقم الصف
 * @param {Object} headers - خريطة العناوين
 * @param {string} state - الحالة المراد البحث عنها
 * @returns {boolean} true إذا كانت الحالة موجودة
 */
function hasSuccessState(sheet, rowNum, headers, state) {
  try {
    const successStatesCol = headers['حالات النجاح'];
    if (!successStatesCol) {
      return false;
    }

    const currentValue = sheet.getRange(rowNum, successStatesCol).getValue();
    if (!currentValue || typeof currentValue !== 'string') {
      return false;
    }

    const statesArray = currentValue.split(',').map(s => s.trim()).filter(s => s);
    return statesArray.includes(state);

  } catch (error) {
    Logger.log('خطأ في البحث عن حالة النجاح: ' + error.toString());
    return false;
  }
}

/**
 * تحديد نوع البيان المدخل
 *
 * @param {Object} formData - بيانات النموذج
 * @param {Object} headers - خريطة العناوين
 * @returns {string} نوع البيان
 */
function determineStatementType(formData, headers) {
  try {
    Logger.log('تحديد نوع البيان المدخل');

    // أسماء الحقول من الإعدادات
    const properties = PropertiesService.getScriptProperties();
    const multiValueField = properties.getProperty('MULTI_VALUE_FIELD') || '🪀 بيانات متعددة غير مفصله';
    const singleValueField = properties.getProperty('SINGLE_VALUE_FIELD') || '🪀 بيان عقار غير مفصل';
    const detailedType = properties.getProperty('DETAILED_TYPE') || 'بيان مفصل 💘';

    // فحص نوع البيان
    const typeQuestion = properties.getProperty('PROPERTY_TYPE_QUESTION') || 'نوع البيان اللي بتسجله ❓❔';

    if (formData[typeQuestion]) {
      const statementType = formData[typeQuestion].toString().trim();

      if (statementType.includes('متعددة') || statementType === multiValueField) {
        Logger.log('نوع البيان: بيانات متعددة');
        return 'multiple';
      } else if (statementType.includes('غير مفصل') || statementType === singleValueField) {
        Logger.log('نوع البيان: بيان واحد غير مفصل');
        return 'single';
      } else if (statementType.includes('مفصل') || statementType === detailedType) {
        Logger.log('نوع البيان: بيان مفصل');
        return 'detailed';
      }
    }

    // إذا لم يتم تحديد النوع، فحص محتوى البيانات
    const multiField = formData[multiValueField];
    const singleField = formData[singleValueField];

    if (multiField && multiField.toString().trim()) {
      Logger.log('تم اكتشاف بيانات متعددة من المحتوى');
      return 'multiple';
    } else if (singleField && singleField.toString().trim()) {
      Logger.log('تم اكتشاف بيان واحد من المحتوى');
      return 'single';
    }

    Logger.log('نوع البيان: مفصل (افتراضي)');
    return 'detailed';

  } catch (error) {
    Logger.log('خطأ في تحديد نوع البيان: ' + error.toString());
    return 'detailed';
  }
}

/**
 * التحقق من اكتمال معالجة العقار
 *
 * @param {GoogleAppsScript.Spreadsheet.Sheet} sheet - ورقة العمل
 * @param {number} rowNum - رقم الصف
 * @param {Object} headers - خريطة العناوين
 * @returns {Object} حالة المعالجة
 */
function checkProcessingStatus(sheet, rowNum, headers) {
  try {
    Logger.log(`فحص حالة معالجة العقار - الصف: ${rowNum}`);

    const successStates = ['تخزين', 'تفكيك', 'نوشن', 'زوهو', 'اشعار'];
    const completedStates = [];
    const missingStates = [];

    // فحص كل حالة
    for (const state of successStates) {
      if (hasSuccessState(sheet, rowNum, headers, state)) {
        completedStates.push(state);
      } else {
        missingStates.push(state);
      }
    }

    // تحديد حالة المعالجة
    let processingStatus = 'incomplete';
    let nextStep = missingStates[0] || null;

    if (completedStates.length === successStates.length) {
      processingStatus = 'complete';
      nextStep = null;
    } else if (completedStates.length === 0) {
      processingStatus = 'not_started';
      nextStep = 'تخزين';
    } else {
      processingStatus = 'in_progress';
    }

    Logger.log(`حالة المعالجة: ${processingStatus}, الخطوة التالية: ${nextStep}`);

    return {
      status: processingStatus,
      completedStates: completedStates,
      missingStates: missingStates,
      nextStep: nextStep,
      progress: Math.round((completedStates.length / successStates.length) * 100)
    };

  } catch (error) {
    Logger.log('خطأ في فحص حالة المعالجة: ' + error.toString());
    return {
      status: 'error',
      completedStates: [],
      missingStates: successStates,
      nextStep: 'تخزين',
      progress: 0
    };
  }
}

// ===================================================================
// نهاية ملف validation.js - المرحلة الأولى
// ===================================================================
